// Application constants
export const APP_CONFIG = {
  VERSION: '0.2',
  TITLE: 'Bibliographic Items Tagging Tool',
  DESCRIPTION: 'An AI-driven tool for tagging bibliographic items using tags from a tag pool. (e.g.IsisCB\'s tag pool, etc)'
}

// API Configuration
export const API_ENDPOINTS = {
  GENERATE_TAGS: 'https://tagger.yizehu.com/api/generate_tags',
  ALL_TAGS: 'https://tagger.yizehu.com/api/tags/all'
}

// Model Options
export const MODEL_OPTIONS = [
  { value: 'gpt-4o-mini', label: 'gpt-4o-mini (3~4s/item)' },
  { value: 'pytextrank', label: 'pytextrank (very fast)' }
]

// Zotero Configuration
export const ZOTERO_CONFIG = {
  MAX_ITEMS_PER_REQUEST: 100,
  API_VERSION: '3',
  FREQUENTLY_USED_TAGS: [
    'to-be-tagged',
    'IsisCB project'
  ]
}

// Export Configuration
export const EXPORT_CONFIG = {
  DEFAULT_OPTIONS: {
    fieldDelimiter: ',',
    tagDelimiter: ';',
    valueMarkers: '"'
  },
  DEFAULT_NEW_TAG_MARK: '[NEW]',
  DEFAULT_CUSTOM_SUFFIX: '[IsisCBtag]',
  DEFAULT_PROCESSED_TAG: 'processed-by-tagger'
}

// Pagination Configuration
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [5, 10, 20, 50]
}

// Tag Management
export const TAG_CONFIG = {
  MIN_SEARCH_LENGTH: 2,
  MAX_SUGGESTIONS: 40,
  DEBOUNCE_DELAY: 300
}

// Batch Processing
export const BATCH_CONFIG = {
  calculateSuggestedBatchSize: (itemCount) => {
    if (itemCount === 0) return 1
    if (itemCount <= 5) return itemCount
    if (itemCount <= 50) return Math.ceil(itemCount / 5)
    if (itemCount <= 200) return Math.ceil(itemCount / 10)
    return Math.ceil(itemCount / 20)
  }
}
