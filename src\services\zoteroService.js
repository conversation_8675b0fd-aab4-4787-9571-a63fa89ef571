import axios from 'axios'
import { ZOTERO_CONFIG } from '../utils/constants.js'

/**
 * Zotero API Service
 * Handles all interactions with the Zotero API
 */
class ZoteroService {
  constructor() {
    this.baseUrl = ''
    this.isGroupLibrary = true
  }

  /**
   * Test library access and determine if it's a group or personal library
   * @param {string} libraryId - Library ID
   * @param {string} apiKey - API key
   * @param {string} tag - Tag to search for
   * @returns {Promise<boolean>} - Whether the library is accessible
   */
  async testLibraryAccess(libraryId, apiKey, tag) {
    // Try group library first
    this.baseUrl = `https://api.zotero.org/groups/${libraryId}`
    this.isGroupLibrary = true

    try {
      const testResponse = await axios.get(`${this.baseUrl}/items`, {
        params: {
          tag,
          limit: 1,
          includeTrashed: 0
        },
        headers: {
          'Zotero-API-Version': ZOTERO_CONFIG.API_VERSION,
          'Authorization': `Bearer ${apiKey}`
        }
      })

      // If group library returns empty, try personal library
      if (Array.isArray(testResponse.data) && testResponse.data.length === 0) {
        this.baseUrl = `https://api.zotero.org/users/${libraryId}`
        this.isGroupLibrary = false

        const personalTestResponse = await axios.get(`${this.baseUrl}/items`, {
          params: {
            tag,
            limit: 1,
            includeTrashed: 0
          },
          headers: {
            'Zotero-API-Version': ZOTERO_CONFIG.API_VERSION,
            'Authorization': `Bearer ${apiKey}`
          }
        })

        if (Array.isArray(personalTestResponse.data) && personalTestResponse.data.length === 0) {
          throw new Error('Both group and personal libraries returned empty lists.')
        }
      }

      return true
    } catch (error) {
      if (error.response && error.response.status === 403) {
        throw new Error('No item with the given tag found. Please make sure all items are synced to the online library, and check your library ID and API key permissions.')
      }
      throw new Error('Failed to connect to Zotero library. Please check your configuration.')
    }
  }

  /**
   * Get total count of items with a specific tag
   * @param {string} apiKey - API key
   * @param {string} tag - Tag to search for
   * @returns {Promise<number>} - Total number of items
   */
  async getTotalItemsCount(apiKey, tag) {
    const response = await axios.get(`${this.baseUrl}/items`, {
      params: {
        tag,
        limit: 1,
        includeTrashed: 0
      },
      headers: {
        'Zotero-API-Version': ZOTERO_CONFIG.API_VERSION,
        'Authorization': `Bearer ${apiKey}`
      }
    })

    return parseInt(response.headers['total-results'])
  }

  /**
   * Fetch items from Zotero library
   * @param {string} apiKey - API key
   * @param {string} tag - Tag to search for
   * @param {number} limit - Number of items per request
   * @param {number} start - Starting index
   * @returns {Promise<Array>} - Array of items
   */
  async fetchItems(apiKey, tag, limit = ZOTERO_CONFIG.MAX_ITEMS_PER_REQUEST, start = 0) {
    const response = await axios.get(`${this.baseUrl}/items`, {
      params: {
        tag,
        limit,
        start,
        includeTrashed: 0,
        itemType: '-attachment || note' // Exclude attachments and notes
      },
      headers: {
        'Zotero-API-Version': ZOTERO_CONFIG.API_VERSION,
        'Authorization': `Bearer ${apiKey}`
      }
    })

    return response.data.map(item => ({
      key: item.key,
      version: item.version,
      title: item.data.title || '',
      abstract: item.data.abstractNote || '',
      creators: item.data.creators || [],
      itemType: item.data.itemType || '',
      date: item.data.date || '',
      publicationTitle: item.data.publicationTitle || '',
      volume: item.data.volume || '',
      issue: item.data.issue || '',
      pages: item.data.pages || '',
      url: item.data.url || '',
      extra: item.data.extra || '',
    })).filter(item => item.title || item.abstract)
  }

  /**
   * Fetch all items with a specific tag
   * @param {string} libraryId - Library ID
   * @param {string} apiKey - API key
   * @param {string} tag - Tag to search for
   * @param {Function} progressCallback - Progress callback function
   * @returns {Promise<Array>} - Array of all items
   */
  async fetchAllItems(libraryId, apiKey, tag, progressCallback = null) {
    // Test library access first
    await this.testLibraryAccess(libraryId, apiKey, tag)

    // Get total items count
    const totalItems = await this.getTotalItemsCount(apiKey, tag)
    const numberOfRequests = Math.ceil(totalItems / ZOTERO_CONFIG.MAX_ITEMS_PER_REQUEST)
    
    let allFetchedItems = []

    // Create requests for all batches
    const requests = Array.from({ length: numberOfRequests }, (_, index) => {
      return this.fetchItems(
        apiKey,
        tag,
        ZOTERO_CONFIG.MAX_ITEMS_PER_REQUEST,
        index * ZOTERO_CONFIG.MAX_ITEMS_PER_REQUEST
      )
    })

    // Execute all requests in parallel
    const responses = await Promise.all(requests)

    // Process all responses
    responses.forEach((items, index) => {
      allFetchedItems = [...allFetchedItems, ...items]
      if (progressCallback) {
        const progress = Math.round(((index + 1) / numberOfRequests) * 100)
        progressCallback(progress)
      }
    })

    return allFetchedItems
  }

  /**
   * Get current item data from Zotero
   * @param {string} apiKey - API key
   * @param {string} itemKey - Item key
   * @returns {Promise<Object>} - Item data
   */
  async getCurrentItem(apiKey, itemKey) {
    const response = await axios.get(`${this.baseUrl}/items/${itemKey}`, {
      headers: {
        'Zotero-API-Version': ZOTERO_CONFIG.API_VERSION,
        'Authorization': `Bearer ${apiKey}`
      }
    })

    return response.data
  }

  /**
   * Update item tags in Zotero
   * @param {string} apiKey - API key
   * @param {string} itemKey - Item key
   * @param {Array} tags - Array of tag objects
   * @param {number} version - Item version for conflict resolution
   * @returns {Promise<void>}
   */
  async updateItemTags(apiKey, itemKey, tags, version) {
    await axios.patch(
      `${this.baseUrl}/items/${itemKey}`,
      { tags },
      {
        headers: {
          'Zotero-API-Version': ZOTERO_CONFIG.API_VERSION,
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'If-Unmodified-Since-Version': version
        }
      }
    )
  }

  /**
   * Process and save tags to multiple Zotero items
   * @param {string} libraryId - Library ID
   * @param {string} apiKey - API key
   * @param {Array} results - Results array with tagged items
   * @param {Array} allIndexedBiblioItems - All bibliographic items
   * @param {Object} tagOptions - Tag processing options
   * @returns {Promise<Object>} - Success and failure counts
   */
  async saveTagsToItems(libraryId, apiKey, results, allIndexedBiblioItems, tagOptions) {
    let successCount = 0
    let failureCount = 0

    const updatePromises = results.map(async (result) => {
      const item = allIndexedBiblioItems.find(item => item.index === result.index)
      if (!item?.key || !item?.version) return null

      try {
        // Get current item data
        const currentItemData = await this.getCurrentItem(apiKey, item.key)

        // Process tags according to options
        const processedTags = this.processTagsForSaving(
          currentItemData.data.tags,
          result,
          tagOptions
        )

        // Update the item
        await this.updateItemTags(apiKey, item.key, processedTags, currentItemData.version)
        successCount++
      } catch (error) {
        console.error(`Error updating item ${item.key}:`, error)
        failureCount++
        throw error
      }
    })

    await Promise.all(updatePromises)

    return { successCount, failureCount }
  }

  /**
   * Process tags for saving to Zotero
   * @param {Array} existingTags - Existing tags from Zotero
   * @param {Object} result - Result object with tags
   * @param {Object} options - Tag processing options
   * @returns {Array} - Processed tags array
   */
  processTagsForSaving(existingTags, result, options) {
    const {
      importTag,
      isTagDeselected,
      newTags,
      allTagCandidates,
      metadataSuffixEnabled,
      selectedMetadataFields,
      customSuffixEnabled,
      customSuffix,
      newTagMarkEnabled,
      newTagMarkPosition,
      newTagMark,
      processedTagEnabled,
      processedTag
    } = options

    // Filter existing tags (exclude import tag and previously added suffixes)
    const filteredExistingTags = existingTags
      .filter(tagObj => {
        const tag = tagObj.tag
        return tag !== importTag &&
          (!customSuffixEnabled || !tag.endsWith(customSuffix)) &&
          (!processedTagEnabled || tag !== processedTag)
      })
      .map(tagObj => tagObj.tag)

    // Process matched tags
    const decoratedMatchedTags = result.tags.matched_tags
      .filter(tag => !isTagDeselected(result.index, tag))
      .map(tagName => this.decorateTag(tagName, allTagCandidates, {
        metadataSuffixEnabled,
        selectedMetadataFields,
        customSuffixEnabled,
        customSuffix
      }))

    // Process new tags
    const decoratedNewTags = Array.from(newTags.get(result.index) || [])
      .map(tag => {
        let tagText = tag.text
        
        // Add new tag mark if enabled
        if (newTagMarkEnabled && !tag.isMatched) {
          if (newTagMarkPosition === 'prefix') {
            tagText = `${newTagMark}${tagText}`
          } else if (newTagMarkPosition === 'suffix') {
            tagText = `${tagText}${newTagMark}`
          }
        }

        return this.decorateTag(tagText, allTagCandidates, {
          metadataSuffixEnabled,
          selectedMetadataFields,
          customSuffixEnabled,
          customSuffix
        })
      })

    // Combine all tags
    const allTags = [...new Set([
      ...filteredExistingTags,
      ...decoratedMatchedTags,
      ...decoratedNewTags
    ])]

    // Add processed tag if enabled
    if (processedTagEnabled && processedTag) {
      allTags.push(processedTag)
    }

    // Format for Zotero API
    return allTags.map(tag => ({ tag }))
  }

  /**
   * Decorate a tag with metadata and custom suffixes
   * @param {string} tagText - Original tag text
   * @param {Array} allTagCandidates - All available tags
   * @param {Object} options - Decoration options
   * @returns {string} - Decorated tag
   */
  decorateTag(tagText, allTagCandidates, options) {
    const {
      metadataSuffixEnabled,
      selectedMetadataFields,
      customSuffixEnabled,
      customSuffix
    } = options

    let decoratedTag = tagText
    const matchingTag = allTagCandidates.find(t => t.name === tagText)

    // Add metadata suffix if enabled
    if (metadataSuffixEnabled && matchingTag) {
      const metadataSuffix = selectedMetadataFields
        .map(field => `${matchingTag[field]}`)
        .filter(Boolean)
        .join('|')
      if (metadataSuffix) {
        decoratedTag = `${decoratedTag} [${metadataSuffix}]`
      }
    }

    // Add custom suffix if enabled
    if (customSuffixEnabled) {
      decoratedTag = `${decoratedTag}${customSuffix}`
    }

    return decoratedTag
  }
}

export default new ZoteroService()
