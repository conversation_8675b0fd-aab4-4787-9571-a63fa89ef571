<template>
  <div class="loading-container" :class="containerClass">
    <el-icon v-if="showIcon" :class="iconClass">
      <Loading />
    </el-icon>
    <div v-if="message" class="loading-message">
      <p>{{ message }}</p>
      <p v-if="showTimer">Time elapsed: {{ elapsedTime }} seconds</p>
      <el-progress 
        v-if="showProgress && progress > 0" 
        :percentage="progress" 
        :format="format => `${format}% Complete`"
        :status="progressStatus" 
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Loading } from '@element-plus/icons-vue'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  message: {
    type: String,
    default: ''
  },
  elapsedTime: {
    type: Number,
    default: 0
  },
  progress: {
    type: Number,
    default: 0
  },
  showIcon: {
    type: Boolean,
    default: true
  },
  showTimer: {
    type: <PERSON>olean,
    default: false
  },
  showProgress: {
    type: <PERSON>olean,
    default: false
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value)
  },
  centered: {
    type: Boolean,
    default: true
  },
  progressStatus: {
    type: String,
    default: 'success',
    validator: (value) => ['success', 'exception', 'warning'].includes(value)
  }
})

const containerClass = computed(() => {
  return {
    'loading-centered': props.centered,
    [`loading-${props.size}`]: true
  }
})

const iconClass = computed(() => {
  return {
    'loading-icon': true,
    'is-loading': props.loading,
    [`loading-icon-${props.size}`]: true
  }
})
</script>

<style scoped>
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-centered {
  justify-content: center;
  text-align: center;
  margin: 20px 0;
  padding: 20px;
  background-color: var(--el-color-primary-light-9);
  border-radius: 4px;
  color: var(--el-text-color-primary);
}

.loading-small {
  gap: 8px;
  margin: 10px 0;
  padding: 10px;
}

.loading-large {
  gap: 24px;
  margin: 30px 0;
  padding: 30px;
}

.loading-icon {
  transition: transform 0.3s;
}

.loading-icon.is-loading {
  animation: rotate 1s linear infinite;
}

.loading-icon-small {
  width: 16px;
  height: 16px;
}

.loading-icon-default {
  width: 24px;
  height: 24px;
}

.loading-icon-large {
  width: 32px;
  height: 32px;
}

.loading-message {
  text-align: center;
}

.loading-message p {
  margin: 8px 0;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
