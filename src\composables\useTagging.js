import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import taggingService from '../services/taggingService.js'
import { BATCH_CONFIG } from '../utils/constants.js'

/**
 * Tagging process management composable
 * Handles the tagging workflow and batch processing
 */
export function useTagging() {
  // State
  const isCSVLoading = ref(false)
  const elapsedCSVTime = ref(0)
  const batchSize = ref(null)
  const results = ref([])

  // Timer management
  let csvTimerInterval = null

  // Computed Properties
  const suggestedBatchSize = computed(() => {
    return (biblioItems) => BATCH_CONFIG.calculateSuggestedBatchSize(biblioItems.length)
  })

  const isProcessing = computed(() => isCSVLoading.value)

  // Timer Methods
  const startTimer = () => {
    elapsedCSVTime.value = 0
    csvTimerInterval = setInterval(() => {
      elapsedCSVTime.value++
    }, 1000)
  }

  const stopTimer = () => {
    if (csvTimerInterval) {
      clearInterval(csvTimerInterval)
      csvTimerInterval = null
    }
  }

  // Main tagging method
  const submitBiblioItems = async (biblioItems, selectedModel, apiUrl) => {
    if (!biblioItems || biblioItems.length === 0) {
      ElMessage.error('No items to process')
      return []
    }

    try {
      isCSVLoading.value = true
      startTimer()

      // Clear previous results
      results.value = []

      // Set custom API URL if provided
      if (apiUrl) {
        taggingService.setApiUrl(apiUrl)
      }

      // Index all items
      const allIndexedBiblioItems = biblioItems.map((item, idx) => ({
        ...item,
        index: idx,
      }))

      // Use the user's chosen batch size or suggested size
      const effectiveBatchSize = batchSize.value || BATCH_CONFIG.calculateSuggestedBatchSize(biblioItems.length)

      // Process items in batches
      const batchResults = await taggingService.processBatches(
        allIndexedBiblioItems,
        selectedModel,
        effectiveBatchSize,
        null, // progress callback handled internally
        (batchNumber, totalBatches, itemsProcessed) => {
          ElMessage.success(`Batch ${batchNumber}/${totalBatches} completed (${itemsProcessed} items tagged)`)
        }
      )

      results.value = batchResults
      ElMessage.success(`Successfully tagged ${batchResults.length} items!`)
      
      return {
        results: batchResults,
        allIndexedBiblioItems
      }

    } catch (error) {
      console.error('Error batch-tagging articles:', error)
      ElMessage.error(`An error occurred while tagging articles: ${error.message}`)
      return { results: [], allIndexedBiblioItems: [] }
    } finally {
      stopTimer()
      isCSVLoading.value = false
    }
  }

  // Validate API configuration
  const validateApiConfiguration = async (apiUrl) => {
    try {
      const isValid = await taggingService.validateApiConfiguration(apiUrl)
      if (isValid) {
        ElMessage.success('API configuration is valid')
      } else {
        ElMessage.error('API configuration is invalid or unreachable')
      }
      return isValid
    } catch (error) {
      console.error('API validation error:', error)
      ElMessage.error('Failed to validate API configuration')
      return false
    }
  }

  // Get available models
  const getAvailableModels = async () => {
    try {
      return await taggingService.getAvailableModels()
    } catch (error) {
      console.error('Error fetching available models:', error)
      // Return default models if API call fails
      return [
        { value: 'gpt-4o-mini', label: 'gpt-4o-mini (3~4s/item)' },
        { value: 'pytextrank', label: 'pytextrank (very fast)' }
      ]
    }
  }

  // Estimate processing time
  const estimateProcessingTime = (itemCount, model) => {
    return taggingService.estimateProcessingTime(itemCount, model)
  }

  // Set batch size
  const setBatchSize = (size) => {
    batchSize.value = size
  }

  // Reset state
  const resetTaggingState = () => {
    results.value = []
    batchSize.value = null
    elapsedCSVTime.value = 0
    stopTimer()
  }

  // Cleanup on unmount
  const cleanup = () => {
    stopTimer()
  }

  return {
    // State
    isCSVLoading,
    elapsedCSVTime,
    batchSize,
    results,

    // Computed
    suggestedBatchSize,
    isProcessing,

    // Methods
    submitBiblioItems,
    validateApiConfiguration,
    getAvailableModels,
    estimateProcessingTime,
    setBatchSize,
    resetTaggingState,
    cleanup
  }
}
