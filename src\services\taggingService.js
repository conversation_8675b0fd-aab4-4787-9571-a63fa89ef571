import axios from 'axios'
import { API_ENDPOINTS } from '../utils/constants.js'

/**
 * Tagging API Service
 * Handles all interactions with the tagging API
 */
class TaggingService {
  constructor() {
    this.apiUrl = API_ENDPOINTS.GENERATE_TAGS
  }

  /**
   * Set custom API URL
   * @param {string} url - Custom API URL
   */
  setApiUrl(url) {
    this.apiUrl = url
  }

  /**
   * Generate tags for a batch of items
   * @param {string} model - Model to use for tagging
   * @param {Array} items - Array of items to tag
   * @returns {Promise<Array>} - Array of tagged results
   */
  async generateTags(model, items) {
    try {
      const response = await axios.post(this.apiUrl, {
        model,
        items: items.map(article => ({
          key: article.key,
          title: article.title,
          abstract: article.abstract,
          index: article.index
        }))
      })

      return response.data
    } catch (error) {
      console.error('Error generating tags:', error)
      throw new Error('Failed to generate tags. Please check your API configuration and try again.')
    }
  }

  /**
   * Process items in batches and generate tags
   * @param {Array} allIndexedBiblioItems - All bibliographic items with indices
   * @param {string} model - Model to use for tagging
   * @param {number} batchSize - Size of each batch
   * @param {Function} progressCallback - Callback for progress updates
   * @param {Function} batchCompleteCallback - Callback when each batch completes
   * @returns {Promise<Array>} - Array of all results
   */
  async processBatches(allIndexedBiblioItems, model, batchSize, progressCallback = null, batchCompleteCallback = null) {
    const results = []
    const totalBatches = Math.ceil(allIndexedBiblioItems.length / batchSize)

    for (let i = 0; i < allIndexedBiblioItems.length; i += batchSize) {
      const batch = allIndexedBiblioItems.slice(i, i + batchSize)
      const batchNumber = Math.floor(i / batchSize) + 1

      try {
        const batchResults = await this.generateTags(model, batch)
        results.push(...batchResults)

        if (progressCallback) {
          const progress = Math.round((batchNumber / totalBatches) * 100)
          progressCallback(progress, batchNumber, totalBatches)
        }

        if (batchCompleteCallback) {
          batchCompleteCallback(batchNumber, totalBatches, batchResults.length)
        }
      } catch (error) {
        console.error(`Error processing batch ${batchNumber}:`, error)
        throw new Error(`Failed to process batch ${batchNumber}. ${error.message}`)
      }
    }

    return results
  }

  /**
   * Validate API configuration
   * @param {string} apiUrl - API URL to validate
   * @returns {Promise<boolean>} - Whether the API is accessible
   */
  async validateApiConfiguration(apiUrl) {
    try {
      // Try a simple request to check if the API is accessible
      const response = await axios.get(apiUrl.replace('/generate_tags', '/health'), {
        timeout: 5000
      })
      return response.status === 200
    } catch (error) {
      // If health endpoint doesn't exist, try the main endpoint with minimal data
      try {
        await axios.post(apiUrl, {
          model: 'test',
          items: []
        }, {
          timeout: 5000
        })
        return true
      } catch (error) {
        console.error('API validation failed:', error)
        return false
      }
    }
  }

  /**
   * Get available models from the API
   * @returns {Promise<Array>} - Array of available models
   */
  async getAvailableModels() {
    try {
      const response = await axios.get(this.apiUrl.replace('/generate_tags', '/models'))
      return response.data
    } catch (error) {
      console.warn('Could not fetch available models from API:', error)
      // Return default models if API doesn't support model listing
      return [
        { value: 'gpt-4o-mini', label: 'gpt-4o-mini (3~4s/item)' },
        { value: 'pytextrank', label: 'pytextrank (very fast)' }
      ]
    }
  }

  /**
   * Estimate processing time for a batch
   * @param {number} itemCount - Number of items
   * @param {string} model - Model being used
   * @returns {number} - Estimated time in seconds
   */
  estimateProcessingTime(itemCount, model) {
    const timePerItem = {
      'gpt-4o-mini': 3.5, // 3-4 seconds per item
      'pytextrank': 0.1,  // Very fast
      'default': 2        // Default estimate
    }

    const timePerItemForModel = timePerItem[model] || timePerItem.default
    return Math.ceil(itemCount * timePerItemForModel)
  }

  /**
   * Format items for API request
   * @param {Array} items - Raw items array
   * @returns {Array} - Formatted items for API
   */
  formatItemsForApi(items) {
    return items.map((item, index) => ({
      key: item.key || `item-${index}`,
      title: item.title || '',
      abstract: item.abstract || '',
      index: item.index !== undefined ? item.index : index
    }))
  }
}

export default new TaggingService()
