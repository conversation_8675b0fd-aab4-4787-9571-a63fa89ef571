<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :before-close="handleClose"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
  >
    <div class="dialog-content">
      <el-icon v-if="showIcon" :class="iconClass">
        <component :is="iconComponent" />
      </el-icon>
      <div class="dialog-message">
        <p v-if="message">{{ message }}</p>
        <slot></slot>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel" :disabled="loading">
          {{ cancelText }}
        </el-button>
        <el-button 
          :type="confirmType" 
          @click="handleConfirm" 
          :loading="loading"
          :disabled="loading"
        >
          {{ confirmText }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { Warning, InfoFilled, SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'Confirm'
  },
  message: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'warning',
    validator: (value) => ['warning', 'info', 'success', 'error'].includes(value)
  },
  confirmText: {
    type: String,
    default: 'Confirm'
  },
  cancelText: {
    type: String,
    default: 'Cancel'
  },
  width: {
    type: String,
    default: '30%'
  },
  loading: {
    type: Boolean,
    default: false
  },
  showIcon: {
    type: Boolean,
    default: true
  },
  closeOnClickModal: {
    type: Boolean,
    default: true
  },
  closeOnPressEscape: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel', 'close'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const confirmType = computed(() => {
  const typeMap = {
    warning: 'warning',
    info: 'primary',
    success: 'success',
    error: 'danger'
  }
  return typeMap[props.type] || 'primary'
})

const iconComponent = computed(() => {
  const iconMap = {
    warning: Warning,
    info: InfoFilled,
    success: SuccessFilled,
    error: CircleCloseFilled
  }
  return iconMap[props.type] || Warning
})

const iconClass = computed(() => {
  return {
    'dialog-icon': true,
    [`dialog-icon-${props.type}`]: true
  }
})

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  visible.value = false
  emit('cancel')
}

const handleClose = (done) => {
  emit('close')
  done()
}
</script>

<style scoped>
.dialog-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.dialog-icon {
  font-size: 24px;
  margin-top: 4px;
  flex-shrink: 0;
}

.dialog-icon-warning {
  color: var(--el-color-warning);
}

.dialog-icon-info {
  color: var(--el-color-info);
}

.dialog-icon-success {
  color: var(--el-color-success);
}

.dialog-icon-error {
  color: var(--el-color-danger);
}

.dialog-message {
  flex: 1;
  line-height: 1.6;
}

.dialog-message p {
  margin: 0;
}

.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}
</style>
