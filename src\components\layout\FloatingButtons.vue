<template>
  <div class="floating-button">
    <!-- Expand/Collapse All Button -->
    <div v-if="showExpandToggle">
      <el-button 
        type="primary" 
        circle 
        @click="$emit('toggle-all')"
        :icon="allExpanded ? ZoomOut : ZoomIn"
        :title="allExpanded ? 'Collapse All' : 'Expand All'"
      />
    </div>
    
    <!-- Settings Button -->
    <div>
      <el-button 
        type="primary" 
        circle 
        @click="$emit('open-settings')"
        :icon="Setting"
        title="Advanced Settings"
      />
    </div>

    <!-- Additional Action Buttons -->
    <slot></slot>
  </div>
</template>

<script setup>
import { ZoomIn, ZoomOut, Setting } from '@element-plus/icons-vue'

const props = defineProps({
  showExpandToggle: {
    type: Boolean,
    default: false
  },
  allExpanded: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['toggle-all', 'open-settings'])
</script>

<style scoped>
.floating-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 10px;
}

.floating-button .el-button {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.floating-button .el-button:hover {
  transform: scale(1.05);
  transition: transform 0.2s;
}
</style>
