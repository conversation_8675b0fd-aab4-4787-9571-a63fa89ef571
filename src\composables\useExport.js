import { ref, computed } from 'vue'
import { generateCSVContent, downloadCSV } from '../utils/csvUtils.js'
import { getPreviewSuffix } from '../utils/tagUtils.js'
import { EXPORT_CONFIG } from '../utils/constants.js'

/**
 * Export functionality composable
 * Handles CSV export configuration and generation
 */
export function useExport() {
  // Export Dialog State
  const showExportDialog = ref(false)
  const selectAll = ref(false)

  // Export Fields Configuration
  const exportFields = ref([])
  const includeConceptTags = ref(true)
  const includePersonOrgTags = ref(true)
  const includeTimePlaceTags = ref(true)

  // Export Options
  const exportOptions = ref({
    fieldDelimiter: EXPORT_CONFIG.DEFAULT_OPTIONS.fieldDelimiter,
    tagDelimiter: EXPORT_CONFIG.DEFAULT_OPTIONS.tagDelimiter,
    valueMarkers: EXPORT_CONFIG.DEFAULT_OPTIONS.valueMarkers
  })

  // Computed Properties
  const availableFields = computed(() => {
    return (allIndexedBiblioItems) => {
      if (!allIndexedBiblioItems || allIndexedBiblioItems.length === 0) return []

      return Object.keys(allIndexedBiblioItems[0])
        .filter(key => key !== 'index')
        .filter(key => allIndexedBiblioItems.some(item => {
          const value = item[key]
          return value !== null && value !== undefined && String(value).trim() !== ''
        }))
    }
  })

  // Methods
  const openExportDialog = (allIndexedBiblioItems) => {
    const fields = availableFields.value(allIndexedBiblioItems)
    exportFields.value = [...fields]
    selectAll.value = true
    showExportDialog.value = true
  }

  const handleSelectAll = (val) => {
    if (val) {
      // This will be set when opening the dialog
    } else {
      exportFields.value = []
    }
  }

  const handleExport = (results, allIndexedBiblioItems, tagOptions) => {
    downloadCSVData(results, allIndexedBiblioItems, tagOptions)
    showExportDialog.value = false
  }

  const downloadCSVData = (results, allIndexedBiblioItems, tagOptions) => {
    // If no fields selected, use all available fields
    if (exportFields.value.length === 0) {
      exportFields.value = availableFields.value(allIndexedBiblioItems)
    }

    const csvOptions = {
      exportFields: exportFields.value,
      exportOptions: exportOptions.value,
      includeConceptTags: includeConceptTags.value,
      includePersonOrgTags: includePersonOrgTags.value,
      includeTimePlaceTags: includeTimePlaceTags.value,
      ...tagOptions
    }

    const csvContent = generateCSVContent(results, allIndexedBiblioItems, csvOptions)
    downloadCSV(csvContent, 'tagged_items.csv')
  }

  const generatePreviewSuffix = (metadataSuffixEnabled, selectedMetadataFields) => {
    return getPreviewSuffix(metadataSuffixEnabled, selectedMetadataFields)
  }

  const resetExportOptions = () => {
    exportOptions.value = {
      fieldDelimiter: EXPORT_CONFIG.DEFAULT_OPTIONS.fieldDelimiter,
      tagDelimiter: EXPORT_CONFIG.DEFAULT_OPTIONS.tagDelimiter,
      valueMarkers: EXPORT_CONFIG.DEFAULT_OPTIONS.valueMarkers
    }
    exportFields.value = []
    includeConceptTags.value = true
    includePersonOrgTags.value = true
    includeTimePlaceTags.value = true
    selectAll.value = false
  }

  const setExportFields = (fields) => {
    exportFields.value = [...fields]
    selectAll.value = fields.length > 0
  }

  const setExportOptions = (options) => {
    exportOptions.value = { ...exportOptions.value, ...options }
  }

  const setTagInclusions = (concept, personOrg, timePlace) => {
    includeConceptTags.value = concept
    includePersonOrgTags.value = personOrg
    includeTimePlaceTags.value = timePlace
  }

  return {
    // State
    showExportDialog,
    selectAll,
    exportFields,
    includeConceptTags,
    includePersonOrgTags,
    includeTimePlaceTags,
    exportOptions,

    // Computed
    availableFields,

    // Methods
    openExportDialog,
    handleSelectAll,
    handleExport,
    downloadCSVData,
    generatePreviewSuffix,
    resetExportOptions,
    setExportFields,
    setExportOptions,
    setTagInclusions
  }
}
