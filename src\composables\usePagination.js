import { ref, computed } from 'vue'
import { PAGINATION_CONFIG } from '../utils/constants.js'

/**
 * Pagination management composable
 * Handles pagination state and logic for results display
 */
export function usePagination(items) {
  // State
  const currentPage = ref(1)
  const pageSize = ref(PAGINATION_CONFIG.DEFAULT_PAGE_SIZE)
  const activeNames = ref([])

  // Computed Properties
  const paginatedItems = computed(() => {
    if (!items.value || items.value.length === 0) return []
    
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    return items.value.slice(startIndex, endIndex)
  })

  const totalItems = computed(() => items.value ? items.value.length : 0)

  const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value))

  const hasMultiplePages = computed(() => totalPages.value > 1)

  const currentPageInfo = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value + 1
    const end = Math.min(currentPage.value * pageSize.value, totalItems.value)
    return { start, end, total: totalItems.value }
  })

  // Methods
  const handleSizeChange = (newSize) => {
    pageSize.value = newSize
    currentPage.value = 1 // Reset to first page when changing page size
    activeNames.value = [] // Clear expanded items
  }

  const handleCurrentChange = (newPage) => {
    currentPage.value = newPage
    activeNames.value = [] // Clear expanded items when changing page
  }

  const goToFirstPage = () => {
    currentPage.value = 1
    activeNames.value = []
  }

  const goToLastPage = () => {
    currentPage.value = totalPages.value
    activeNames.value = []
  }

  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page
      activeNames.value = []
    }
  }

  const toggleAllItems = () => {
    if (activeNames.value.length === paginatedItems.value.length) {
      activeNames.value = []
    } else {
      activeNames.value = paginatedItems.value.map(item => item.index)
    }
  }

  const expandAllItems = () => {
    activeNames.value = paginatedItems.value.map(item => item.index)
  }

  const collapseAllItems = () => {
    activeNames.value = []
  }

  const isItemExpanded = (itemIndex) => {
    return activeNames.value.includes(itemIndex)
  }

  const toggleItem = (itemIndex) => {
    const index = activeNames.value.indexOf(itemIndex)
    if (index > -1) {
      activeNames.value.splice(index, 1)
    } else {
      activeNames.value.push(itemIndex)
    }
  }

  const expandItem = (itemIndex) => {
    if (!activeNames.value.includes(itemIndex)) {
      activeNames.value.push(itemIndex)
    }
  }

  const collapseItem = (itemIndex) => {
    const index = activeNames.value.indexOf(itemIndex)
    if (index > -1) {
      activeNames.value.splice(index, 1)
    }
  }

  // Reset pagination state
  const resetPagination = () => {
    currentPage.value = 1
    pageSize.value = PAGINATION_CONFIG.DEFAULT_PAGE_SIZE
    activeNames.value = []
  }

  // Set page size options
  const pageSizeOptions = PAGINATION_CONFIG.PAGE_SIZE_OPTIONS

  return {
    // State
    currentPage,
    pageSize,
    activeNames,

    // Computed
    paginatedItems,
    totalItems,
    totalPages,
    hasMultiplePages,
    currentPageInfo,

    // Constants
    pageSizeOptions,

    // Methods
    handleSizeChange,
    handleCurrentChange,
    goToFirstPage,
    goToLastPage,
    goToPage,
    toggleAllItems,
    expandAllItems,
    collapseAllItems,
    isItemExpanded,
    toggleItem,
    expandItem,
    collapseItem,
    resetPagination
  }
}
