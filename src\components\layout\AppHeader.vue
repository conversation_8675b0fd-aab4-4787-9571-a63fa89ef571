<template>
  <el-card class="app-container">
    <template #header>
      <div class="header-content">
        <h1 class="header-title">{{ title }}</h1>
        <el-text class="header-description">{{ description }}</el-text>
      </div>
    </template>
    
    <slot></slot>
  </el-card>
</template>

<script setup>
import { APP_CONFIG } from '../../utils/constants.js'

const props = defineProps({
  title: {
    type: String,
    default: APP_CONFIG.TITLE
  },
  description: {
    type: String,
    default: `${APP_CONFIG.DESCRIPTION} (Ver ${APP_CONFIG.VERSION})`
  }
})
</script>

<style scoped>
.app-container {
  width: 100%;
  height: 100%;
  margin: auto auto;
}

.header-content {
  text-align: center;
}

.header-title {
  margin: 0;
  font-size: 24px;
  color: var(--el-text-color-primary);
}

.header-description {
  display: block;
  margin-top: 8px;
  color: var(--el-text-color-regular);
}

:deep(.el-card__header) {
  text-align: center;
  padding: 10px;
  background-color: var(--el-color-primary-light-9);
}
</style>
