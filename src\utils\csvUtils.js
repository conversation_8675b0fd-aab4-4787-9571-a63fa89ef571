import <PERSON> from 'papaparse'
import { toRaw, isReactive } from 'vue'

/**
 * Parse CSV file and extract bibliographic items
 * @param {File} file - The CSV file to parse
 * @param {boolean} hasHeader - Whether the CSV has a header row
 * @returns {Promise<Array>} - Array of parsed items
 */
export function parseCSVFile(file, hasHeader = true) {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      complete: (result) => {
        try {
          let data
          if (hasHeader) {
            // If the CSV has a header, use named fields
            data = result.data
              .filter(row => row.Key) // Ensure the row has a Key (or any required field)
              .map((row) => ({
                key: row.Key?.replace(/^"|"$/g, '').trim() || '',
                itemType: row['Item Type']?.replace(/^"|"$/g, '').trim() || '',
                publicationYear: row['Publication Year']?.replace(/^"|"$/g, '').trim() || '',
                author: row.Author?.replace(/^"|"$/g, '').trim() || '',
                title: row.Title?.replace(/^"|"$/g, '').trim() || '',
                publicationTitle: row['Publication Title']?.replace(/^"|"$/g, '').trim() || '',
                isbn: row.ISBN?.replace(/^"|"$/g, '').trim() || '',
                issn: row.ISSN?.replace(/^"|"$/g, '').trim() || '',
                doi: row.DOI?.replace(/^"|"$/g, '').trim() || '',
                url: row.Url?.replace(/^"|"$/g, '').trim() || '',
                abstract: row['Abstract Note']?.replace(/^"|"$/g, '').trim() || '',
                date: row.Date?.replace(/^"|"$/g, '').trim() || '',
                dateAdded: row['Date Added']?.replace(/^"|"$/g, '').trim() || '',
                dateModified: row['Date Modified']?.replace(/^"|"$/g, '').trim() || '',
                accessDate: row['Access Date']?.replace(/^"|"$/g, '').trim() || '',
                pages: row.Pages?.replace(/^"|"$/g, '').trim() || '',
                numPages: row['Num Pages']?.replace(/^"|"$/g, '').trim() || '',
                issue: row.Issue?.replace(/^"|"$/g, '').trim() || '',
                volume: row.Volume?.replace(/^"|"$/g, '').trim() || '',
                numberOfVolumes: row['Number Of Volumes']?.replace(/^"|"$/g, '').trim() || '',
                journalAbbreviation: row['Journal Abbreviation']?.replace(/^"|"$/g, '').trim() || '',
                shortTitle: row['Short Title']?.replace(/^"|"$/g, '').trim() || '',
                series: row.Series?.replace(/^"|"$/g, '').trim() || '',
                seriesNumber: row['Series Number']?.replace(/^"|"$/g, '').trim() || '',
                seriesText: row['Series Text']?.replace(/^"|"$/g, '').trim() || '',
                seriesTitle: row['Series Title']?.replace(/^"|"$/g, '').trim() || '',
                publisher: row.Publisher?.replace(/^"|"$/g, '').trim() || '',
                place: row.Place?.replace(/^"|"$/g, '').trim() || '',
                language: row.Language?.replace(/^"|"$/g, '').trim() || '',
                rights: row.Rights?.replace(/^"|"$/g, '').trim() || '',
                type: row.Type?.replace(/^"|"$/g, '').trim() || '',
                archive: row.Archive?.replace(/^"|"$/g, '').trim() || '',
                archiveLocation: row['Archive Location']?.replace(/^"|"$/g, '').trim() || '',
                libraryCatalog: row['Library Catalog']?.replace(/^"|"$/g, '').trim() || '',
                callNumber: row['Call Number']?.replace(/^"|"$/g, '').trim() || '',
                extra: row.Extra?.replace(/^"|"$/g, '').trim() || '',
                notes: row.Notes?.replace(/^"|"$/g, '').trim() || '',
                fileAttachments: row['File Attachments']?.replace(/^"|"$/g, '').trim() || '',
                linkAttachments: row['Link Attachments']?.replace(/^"|"$/g, '').trim() || '',
                editor: row.Editor?.replace(/^"|"$/g, '').trim() || '',
                seriesEditor: row['Series Editor']?.replace(/^"|"$/g, '').trim() || '',
                translator: row.Translator?.replace(/^"|"$/g, '').trim() || '',
                contributor: row.Contributor?.replace(/^"|"$/g, '').trim() || ''
              }))
          } else {
            // If no header, use first two columns as title and abstract
            data = result.data
              .filter(row => row.length >= 2 && (row[0] || row[1]))
              .map((row, index) => ({
                title: row[0]?.trim() || '',
                abstract: row[1]?.trim() || '',
                key: `manual-${index}`
              }))
          }
          resolve(data)
        } catch (error) {
          reject(error)
        }
      },
      error: (error) => {
        reject(error)
      }
    })
  })
}

/**
 * Generate CSV content from results data
 * @param {Array} results - The results array
 * @param {Array} allIndexedBiblioItems - The indexed bibliographic items
 * @param {Object} options - Export options
 * @returns {string} - CSV content
 */
export function generateCSVContent(results, allIndexedBiblioItems, options) {
  const {
    exportFields,
    exportOptions,
    includeConceptTags,
    includePersonOrgTags,
    includeTimePlaceTags,
    isTagDeselected,
    newTags,
    allTagCandidates,
    metadataSuffixEnabled,
    selectedMetadataFields,
    customSuffixEnabled,
    customSuffix,
    newTagMarkEnabled,
    newTagMarkPosition,
    newTagMark,
    processedTagEnabled,
    processedTag
  } = options

  const { fieldDelimiter, tagDelimiter, valueMarkers } = exportOptions
  const startMarker = valueMarkers || ''
  const endMarker = valueMarkers || startMarker

  const csvData = results.map(result => {
    const item = allIndexedBiblioItems.find(item => item.index === result.index)
    const row = exportFields.map(field => {
      let fieldValue = item[field] != null ? item[field] : ''

      // Handle reactive objects and arrays
      if (isReactive(fieldValue) || Array.isArray(fieldValue)) {
        const rawFieldValue = toRaw(fieldValue)

        if (Array.isArray(rawFieldValue)) {
          fieldValue = rawFieldValue
            .map(entry => {
              if (entry.lastName || entry.firstName) {
                const lastName = entry.lastName || ''
                const firstName = entry.firstName || ''
                return `${lastName},${firstName}`.trim()
              }
              return ''
            })
            .filter(Boolean)
            .join(tagDelimiter)
        }
      } else if (typeof fieldValue === 'object') {
        fieldValue = Object.values(fieldValue).join(tagDelimiter)
      } else if (typeof fieldValue !== 'string') {
        fieldValue = String(fieldValue)
      }
      return `${startMarker}${fieldValue}${endMarker}`
    })

    // Process matched tags
    const decoratedActiveMatchedTags = result.tags.matched_tags
      .filter(tag => !isTagDeselected(result.index, tag))
      .map(tagText => {
        const matchingTag = allTagCandidates.find(t => t.name === tagText)
        let decoratedTagText = tagText

        // Add metadata suffix if enabled
        if (metadataSuffixEnabled && matchingTag) {
          const metadataSuffix = selectedMetadataFields
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|')
          if (metadataSuffix) {
            decoratedTagText = `${tagText} [${metadataSuffix}]`
          }
        }

        // Add custom suffix if enabled
        if (customSuffixEnabled) {
          decoratedTagText = `${decoratedTagText}${customSuffix}`
        }

        return decoratedTagText
      })

    // Process new tags
    const decoratedNewTags = Array.from(newTags.get(result.index) || [])
      .map(tag => {
        let decoratedNewTagText = tag.text
        if (newTagMarkEnabled && !tag.isMatched) {
          if (newTagMarkPosition === 'prefix') {
            decoratedNewTagText = `${newTagMark}${tag.text}`
          } else if (newTagMarkPosition === 'suffix') {
            decoratedNewTagText = `${tag.text}${newTagMark}`
          }
        }

        const matchingTag = allTagCandidates.find(t => t.name === tag.text)

        // Add metadata suffix if enabled
        if (metadataSuffixEnabled && matchingTag) {
          const metadataSuffix = selectedMetadataFields
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|')
          if (metadataSuffix) {
            decoratedNewTagText = `${decoratedNewTagText} [${metadataSuffix}]`
          }
        }

        // Add custom suffix if enabled
        if (customSuffixEnabled) {
          decoratedNewTagText = `${decoratedNewTagText}${customSuffix}`
        }

        return decoratedNewTagText
      })

    // Combine all tags
    const allDecoratedTags = [...new Set([...decoratedActiveMatchedTags, ...decoratedNewTags])]
    if (processedTagEnabled && processedTag) {
      allDecoratedTags.push(processedTag)
    }

    // Add combined tags to row
    row.push(`${startMarker}${allDecoratedTags.join(tagDelimiter)}${endMarker}`)

    // Add other tag categories if selected
    if (includeConceptTags) row.push(`${startMarker}${result.tags.concept_tags.join(tagDelimiter)}${endMarker}`)
    if (includePersonOrgTags) row.push(`${startMarker}${result.tags.person_org_tags.join(tagDelimiter)}${endMarker}`)
    if (includeTimePlaceTags) row.push(`${startMarker}${result.tags.time_place_tags.join(tagDelimiter)}${endMarker}`)

    return row.join(fieldDelimiter)
  })

  // Create header row
  const headerRow = [
    ...exportFields,
    'Matched Tags',
    ...(includeConceptTags ? ['Concept Tags'] : []),
    ...(includePersonOrgTags ? ['Person/Org Tags'] : []),
    ...(includeTimePlaceTags ? ['Time/Place Tags'] : [])
  ].map(header => `${startMarker}${header}${endMarker}`)

  // Add header row to csvData
  csvData.unshift(headerRow.join(fieldDelimiter))

  return csvData.join('\n')
}

/**
 * Download CSV file
 * @param {string} csvContent - The CSV content
 * @param {string} filename - The filename for download
 */
export function downloadCSV(csvContent, filename = 'tagged_items.csv') {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = filename
  link.click()
}
