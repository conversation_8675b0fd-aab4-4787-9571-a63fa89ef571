import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { debounce } from 'lodash'
import tagPoolService from '../services/tagPoolService.js'
import { 
  filterTags, 
  isTagDeselected as checkTagDeselected, 
  toggleTagSelection,
  tagAlreadyExists,
  addNewTag as addNewTagUtil,
  removeNewTag as removeNewTagUtil
} from '../utils/tagUtils.js'
import { TAG_CONFIG } from '../utils/constants.js'

/**
 * Tag management composable
 * Handles tag selection, deselection, and new tag addition
 */
export function useTagManagement() {
  // State
  const deselectedTags = ref(new Set())
  const newTags = ref(new Map())
  const inputNewTagValue = ref('')
  const searchQuery = ref(new Map())
  const showTagSuggestions = ref(new Map())
  const allTagCandidates = ref([])
  const hasLoadedTags = ref(false)
  const availableMetadataFields = ref([])

  // Computed Properties
  const tagNames = computed(() => allTagCandidates.value.map(tag => tag.name || tag))

  // Debounced filter function
  const debouncedFilter = debounce((query, resultIndex) => {
    if (!query || query.length < TAG_CONFIG.MIN_SEARCH_LENGTH) {
      showTagSuggestions.value.set(resultIndex, false)
      return
    }
    showTagSuggestions.value.set(resultIndex, true)
  }, TAG_CONFIG.DEBOUNCE_DELAY)

  // Methods
  const fetchAllTags = async (forceRefresh = false) => {
    if (hasLoadedTags.value && !forceRefresh) return

    try {
      const tags = await tagPoolService.fetchAllTags(forceRefresh)
      allTagCandidates.value = tags
      hasLoadedTags.value = true
      updateMetadataFields()
      ElMessage.success('Tag pool loaded successfully.')
    } catch (error) {
      console.error('Error fetching tags:', error)
      ElMessage.error('Failed to fetch tags')
      allTagCandidates.value = []
    }
  }

  const updateMetadataFields = () => {
    if (!allTagCandidates.value || allTagCandidates.value.length === 0) {
      availableMetadataFields.value = [{ label: 'N/A', value: 'N/A' }]
      return
    }

    const firstObject = allTagCandidates.value[0]
    const fields = Object.keys(firstObject || {})
    const filteredFields = fields.filter(field => field !== 'name')

    availableMetadataFields.value = filteredFields.map(field => ({
      label: field,
      value: field
    }))
  }

  const isTagDeselected = (resultIndex, tag) => {
    return checkTagDeselected(deselectedTags.value, resultIndex, tag)
  }

  const toggleTag = (resultIndex, tag) => {
    toggleTagSelection(deselectedTags.value, resultIndex, tag)
  }

  const getFilteredTags = (query) => {
    return filterTags(tagNames.value, query, TAG_CONFIG.MAX_SUGGESTIONS)
  }

  const handleInputFocus = async (resultIndex) => {
    if (!hasLoadedTags.value) {
      await fetchAllTags()
    }
    showTagSuggestions.value.set(resultIndex, true)
  }

  const handleSearchInput = (value, resultIndex) => {
    searchQuery.value.set(resultIndex, value)
    debouncedFilter(value, resultIndex)
  }

  const addNewTag = (resultIndex, tagText, matchedTags = []) => {
    if (!tagText.trim()) return false

    const trimmedTag = tagText.trim()

    // Check if tag already exists
    const newTagsSet = newTags.value.get(resultIndex)
    const isDeselected = () => isTagDeselected(resultIndex, trimmedTag)
    
    if (tagAlreadyExists(matchedTags, newTagsSet, trimmedTag, isDeselected)) {
      ElMessage.warning('Tag already exists in matched tags.')
      return false
    }

    // Add the tag
    const tagObject = addNewTagUtil(newTags.value, resultIndex, trimmedTag, tagNames.value)
    
    // Clear input and close suggestions
    inputNewTagValue.value = ''
    searchQuery.value.set(resultIndex, '')
    showTagSuggestions.value.set(resultIndex, false)

    return true
  }

  const addNewTagDoubleClick = (resultIndex, tag, matchedTags = []) => {
    if (!tag) return false

    const newTagsSet = newTags.value.get(resultIndex)
    const isDeselected = () => isTagDeselected(resultIndex, tag)
    
    if (tagAlreadyExists(matchedTags, newTagsSet, tag, isDeselected)) {
      ElMessage.warning('Tag already exists in matched tags.')
      return false
    }

    addNewTagUtil(newTags.value, resultIndex, tag, tagNames.value)
    ElMessage.success('New tag added to matched tags!')
    return true
  }

  const removeNewTag = (resultIndex, tagText) => {
    removeNewTagUtil(newTags.value, resultIndex, tagText)
  }

  const selectTag = (resultIndex, tag, matchedTags = []) => {
    const newTagsSet = newTags.value.get(resultIndex)
    const isDeselected = () => isTagDeselected(resultIndex, tag)
    
    if (tagAlreadyExists(matchedTags, newTagsSet, tag, isDeselected)) {
      ElMessage.warning('Tag already exists in matched tags.')
      return false
    }

    addNewTagUtil(newTags.value, resultIndex, tag, tagNames.value)
    
    // Clear input and close suggestions
    inputNewTagValue.value = ''
    searchQuery.value.set(resultIndex, '')
    showTagSuggestions.value.set(resultIndex, false)

    return true
  }

  const closeSuggestions = (resultIndex) => {
    setTimeout(() => {
      showTagSuggestions.value.set(resultIndex, false)
    }, 200)
  }

  // Drag and drop handlers
  const handleDragStart = (event, tag, sourceSection) => {
    event.dataTransfer.setData('text/plain', JSON.stringify({
      tag,
      sourceSection
    }))
  }

  const handleDragOver = (event) => {
    event.preventDefault()
  }

  const handleDrop = (event, resultIndex, matchedTags = []) => {
    event.preventDefault()
    const data = JSON.parse(event.dataTransfer.getData('text/plain'))
    const { tag } = data

    // Add to matched tags if not already present
    if (!matchedTags.includes(tag) || isTagDeselected(resultIndex, tag)) {
      const newTagsSet = newTags.value.get(resultIndex)
      const isDeselected = () => isTagDeselected(resultIndex, tag)
      
      if (tagAlreadyExists(matchedTags, newTagsSet, tag, isDeselected)) {
        ElMessage.warning('Tag already exists in matched tags.')
        return
      }

      addNewTagUtil(newTags.value, resultIndex, tag, tagNames.value)
    } else {
      ElMessage.warning('Tag already exists in matched tags.')
    }
  }

  // Clear all tag management state
  const clearTagManagementState = () => {
    deselectedTags.value = new Set()
    newTags.value = new Map()
    inputNewTagValue.value = ''
    searchQuery.value = new Map()
    showTagSuggestions.value = new Map()
  }

  // Get final tags for a result (for export/save)
  const getFinalTags = (resultIndex, matchedTags) => {
    const activeTags = matchedTags.filter(tag => !isTagDeselected(resultIndex, tag))
    const newTagsSet = newTags.value.get(resultIndex) || new Set()
    const newTagTexts = Array.from(newTagsSet).map(tag => tag.text)
    
    return [...new Set([...activeTags, ...newTagTexts])]
  }

  return {
    // State
    deselectedTags,
    newTags,
    inputNewTagValue,
    searchQuery,
    showTagSuggestions,
    allTagCandidates,
    hasLoadedTags,
    availableMetadataFields,

    // Computed
    tagNames,

    // Methods
    fetchAllTags,
    updateMetadataFields,
    isTagDeselected,
    toggleTag,
    getFilteredTags,
    handleInputFocus,
    handleSearchInput,
    addNewTag,
    addNewTagDoubleClick,
    removeNewTag,
    selectTag,
    closeSuggestions,
    handleDragStart,
    handleDragOver,
    handleDrop,
    clearTagManagementState,
    getFinalTags
  }
}
