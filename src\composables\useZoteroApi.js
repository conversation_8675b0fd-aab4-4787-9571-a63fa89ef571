import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import zoteroService from '../services/zoteroService.js'
import { ZOTERO_CONFIG } from '../utils/constants.js'

/**
 * Zotero API integration composable
 * Manages Zotero configuration and API interactions
 */
export function useZoteroApi() {
  // Zotero Configuration
  const zoteroConfig = ref({
    libraryId: '',
    apiKey: '',
    tag: ''
  })

  const frequentlyUsedTagsForImport = ref(ZOTERO_CONFIG.FREQUENTLY_USED_TAGS)

  // Loading State
  const isZoteroLoading = ref(false)
  const fetchProgress = ref(0)

  // Computed Properties
  const isZoteroConfigValid = computed(() => {
    const { libraryId, apiKey, tag } = zoteroConfig.value
    return libraryId && apiKey && tag
  })

  // Methods
  const updateProgress = (progress) => {
    fetchProgress.value = Math.round(progress)
  }

  const fetchZoteroItems = async () => {
    if (!isZoteroConfigValid.value) {
      ElMessage.error('Please fill in all required Zotero configuration fields')
      return []
    }

    try {
      isZoteroLoading.value = true
      fetchProgress.value = 0

      const { libraryId, apiKey, tag } = zoteroConfig.value

      const items = await zoteroService.fetchAllItems(
        libraryId,
        apiKey,
        tag,
        updateProgress
      )

      ElMessage.success(`Successfully fetched ${items.length} items from Zotero`)
      return items

    } catch (error) {
      console.error('Error fetching Zotero items:', error)
      
      let errorMessage = 'Failed to fetch items from Zotero'
      if (error.message.includes('No item with the given tag found')) {
        errorMessage = 'No item with the given tag found. Please make sure all items are synced to the online library, and check your library ID and API key permissions.'
      } else if (error.message.includes('Failed to connect')) {
        errorMessage = 'Failed to connect to Zotero library. Please check your configuration.'
      } else if (error.response) {
        switch (error.response.status) {
          case 403:
            errorMessage = 'Invalid API key or insufficient permissions'
            break
          case 404:
            errorMessage = 'Library not found. Please check your Library ID'
            break
          case 429:
            errorMessage = 'Too many requests. Please try again later'
            break
          default:
            errorMessage = error.response.data?.message || errorMessage
        }
      }

      ElMessage.error(errorMessage)
      return []
    } finally {
      isZoteroLoading.value = false
      fetchProgress.value = 0
    }
  }

  const saveTagsToZotero = async (results, allIndexedBiblioItems, tagOptions) => {
    if (!isZoteroConfigValid.value) {
      ElMessage.error('Invalid Zotero configuration')
      return false
    }

    try {
      const { libraryId, apiKey } = zoteroConfig.value

      const saveOptions = {
        ...tagOptions,
        importTag: zoteroConfig.value.tag
      }

      const { successCount, failureCount } = await zoteroService.saveTagsToItems(
        libraryId,
        apiKey,
        results,
        allIndexedBiblioItems,
        saveOptions
      )

      if (failureCount === 0) {
        ElMessage.success(`Successfully saved tags to Zotero for all ${successCount} items`)
      } else {
        ElMessage.warning(`Saved tags for ${successCount} items, failed for ${failureCount} items`)
      }

      return failureCount === 0

    } catch (error) {
      console.error('Error saving tags to Zotero:', error)
      
      let errorMessage = 'Failed to save tags to Zotero'
      if (error.response) {
        switch (error.response.status) {
          case 403:
            errorMessage = 'Invalid API key or insufficient permissions'
            break
          case 404:
            errorMessage = 'Library or item not found'
            break
          case 412:
            errorMessage = 'Item was modified since last retrieval. Please refresh and try again'
            break
          case 429:
            errorMessage = 'Too many requests. Please try again later'
            break
          default:
            errorMessage = error.response.data?.message || errorMessage
        }
      }

      ElMessage.error(errorMessage)
      return false
    }
  }

  const validateZoteroConfig = async () => {
    if (!isZoteroConfigValid.value) {
      return false
    }

    try {
      const { libraryId, apiKey, tag } = zoteroConfig.value
      await zoteroService.testLibraryAccess(libraryId, apiKey, tag)
      return true
    } catch (error) {
      console.error('Zotero configuration validation failed:', error)
      return false
    }
  }

  const clearZoteroConfig = () => {
    zoteroConfig.value = {
      libraryId: '',
      apiKey: '',
      tag: ''
    }
  }

  const setZoteroConfig = (config) => {
    zoteroConfig.value = { ...zoteroConfig.value, ...config }
  }

  return {
    // State
    zoteroConfig,
    frequentlyUsedTagsForImport,
    isZoteroLoading,
    fetchProgress,

    // Computed
    isZoteroConfigValid,

    // Methods
    fetchZoteroItems,
    saveTagsToZotero,
    validateZoteroConfig,
    clearZoteroConfig,
    setZoteroConfig,
    updateProgress
  }
}
