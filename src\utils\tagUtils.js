/**
 * Filter tags based on search query
 * @param {Array} tagNames - Array of tag names
 * @param {string} query - Search query
 * @param {number} limit - Maximum number of results
 * @returns {Array} - Filtered and sorted tag names
 */
export function filterTags(tagNames, query, limit = 40) {
  if (!query || query.length < 2) return []

  const searchTerm = query.toLowerCase()
  return tagNames
    .filter(tag => tag.toLowerCase().includes(searchTerm))
    .sort((a, b) => {
      const aLower = a.toLowerCase()
      const bLower = b.toLowerCase()
      const aStartsWith = aLower.startsWith(searchTerm)
      const bStartsWith = bLower.startsWith(searchTerm)
      if (aStartsWith && !bStartsWith) return -1
      if (!aStartsWith && bStartsWith) return 1
      return aLower.indexOf(searchTerm) - bLower.indexOf(searchTerm)
    })
    .slice(0, limit)
}

/**
 * Check if a tag is deselected for a specific result
 * @param {Set} deselectedTags - Set of deselected tags
 * @param {number} resultIndex - Index of the result
 * @param {string} tag - Tag name
 * @returns {boolean} - Whether the tag is deselected
 */
export function isTagDeselected(deselectedTags, resultIndex, tag) {
  return deselectedTags.has(`${resultIndex}-${tag}`)
}

/**
 * Toggle tag selection state
 * @param {Set} deselectedTags - Set of deselected tags
 * @param {number} resultIndex - Index of the result
 * @param {string} tag - Tag name
 */
export function toggleTagSelection(deselectedTags, resultIndex, tag) {
  const tagKey = `${resultIndex}-${tag}`
  if (deselectedTags.has(tagKey)) {
    deselectedTags.delete(tagKey)
  } else {
    deselectedTags.add(tagKey)
  }
}

/**
 * Check if a tag already exists in matched tags or new tags
 * @param {Array} matchedTags - Array of matched tags
 * @param {Set} newTagsSet - Set of new tags for the result
 * @param {string} tag - Tag to check
 * @param {Function} isDeselected - Function to check if tag is deselected
 * @returns {boolean} - Whether the tag already exists
 */
export function tagAlreadyExists(matchedTags, newTagsSet, tag, isDeselected) {
  // Check if tag exists in new tags
  const existsInNewTags = Array.from(newTagsSet || []).some(
    existingTag => existingTag.text === tag
  )
  
  // Check if tag exists in matched tags and is not deselected
  const existsInMatchedTags = matchedTags.includes(tag) && !isDeselected()
  
  return existsInNewTags || existsInMatchedTags
}

/**
 * Add a new tag to the result's tag set
 * @param {Map} newTags - Map of new tags by result index
 * @param {number} resultIndex - Index of the result
 * @param {string} tagText - Text of the tag
 * @param {Array} tagNames - Array of all available tag names
 * @returns {Object} - The created tag object
 */
export function addNewTag(newTags, resultIndex, tagText, tagNames) {
  if (!newTags.has(resultIndex)) {
    newTags.set(resultIndex, new Set())
  }

  const isExistingTag = tagNames.includes(tagText)
  const tagObject = {
    text: tagText,
    isMatched: isExistingTag
  }

  newTags.get(resultIndex).add(tagObject)
  return tagObject
}

/**
 * Remove a new tag from the result's tag set
 * @param {Map} newTags - Map of new tags by result index
 * @param {number} resultIndex - Index of the result
 * @param {string} tagText - Text of the tag to remove
 */
export function removeNewTag(newTags, resultIndex, tagText) {
  const tags = newTags.get(resultIndex)
  if (tags) {
    for (const tag of tags) {
      if (tag.text === tagText) {
        tags.delete(tag)
        break
      }
    }
  }
}

/**
 * Decorate a tag with metadata suffix
 * @param {string} tagText - Original tag text
 * @param {Object} matchingTag - Matching tag object from tag pool
 * @param {boolean} metadataSuffixEnabled - Whether metadata suffix is enabled
 * @param {Array} selectedMetadataFields - Selected metadata fields
 * @returns {string} - Decorated tag text
 */
export function decorateTagWithMetadata(tagText, matchingTag, metadataSuffixEnabled, selectedMetadataFields) {
  let decoratedTag = tagText

  if (metadataSuffixEnabled && matchingTag) {
    const metadataSuffix = selectedMetadataFields
      .map(field => matchingTag[field] != null ? matchingTag[field] : '')
      .filter(Boolean)
      .join('|')
    
    if (metadataSuffix) {
      decoratedTag = `${tagText} [${metadataSuffix}]`
    }
  }

  return decoratedTag
}

/**
 * Decorate a tag with custom suffix
 * @param {string} tagText - Original tag text
 * @param {boolean} customSuffixEnabled - Whether custom suffix is enabled
 * @param {string} customSuffix - Custom suffix text
 * @returns {string} - Decorated tag text
 */
export function decorateTagWithCustomSuffix(tagText, customSuffixEnabled, customSuffix) {
  if (customSuffixEnabled) {
    return `${tagText}${customSuffix}`
  }
  return tagText
}

/**
 * Decorate a new tag with prefix/suffix mark
 * @param {string} tagText - Original tag text
 * @param {boolean} newTagMarkEnabled - Whether new tag mark is enabled
 * @param {string} newTagMarkPosition - Position of the mark ('prefix' or 'suffix')
 * @param {string} newTagMark - Mark text
 * @param {boolean} isMatched - Whether the tag is matched in the tag pool
 * @returns {string} - Decorated tag text
 */
export function decorateNewTag(tagText, newTagMarkEnabled, newTagMarkPosition, newTagMark, isMatched) {
  if (newTagMarkEnabled && !isMatched) {
    if (newTagMarkPosition === 'prefix') {
      return `${newTagMark}${tagText}`
    } else if (newTagMarkPosition === 'suffix') {
      return `${tagText}${newTagMark}`
    }
  }
  return tagText
}

/**
 * Get preview suffix for tag decoration
 * @param {boolean} metadataSuffixEnabled - Whether metadata suffix is enabled
 * @param {Array} selectedMetadataFields - Selected metadata fields
 * @returns {string} - Preview suffix
 */
export function getPreviewSuffix(metadataSuffixEnabled, selectedMetadataFields) {
  if (metadataSuffixEnabled && selectedMetadataFields.length > 0) {
    return `[${selectedMetadataFields.join('|')}]`
  }
  return ''
}
