<template>
  <div class="data-import-section">
    <el-divider class="el-divider--nowrap">Step 1. Import Bibliographic Items</el-divider>
    
    <el-row justify="center">
      <el-col :span="12" :xs="24">
        <el-collapse v-model="activeDataSource">
          <!-- Zotero Import -->
          <el-collapse-item name="zotero">
            <template #title>
              <div class="source-title truncate-title">
                <el-icon><Download /></el-icon>
                Import from Zotero (Recommended)
              </div>
            </template>
            
            <ZoteroImport
              v-model:config="zoteroConfig"
              :loading="isZoteroLoading"
              :progress="fetchProgress"
              @fetch="$emit('fetch-zotero', $event)"
            />
          </el-collapse-item>

          <!-- CSV Upload -->
          <el-collapse-item name="csv-upload" class="center-content">
            <template #title>
              <div class="source-title truncate-title">
                <el-icon><Upload /></el-icon>
                Upload CSV
              </div>
            </template>
            
            <CsvUpload
              v-model:has-header="csvHasZoteroHeader"
              @upload="$emit('csv-upload', $event)"
            />
          </el-collapse-item>

          <!-- Manual Input -->
          <el-collapse-item name="manual-input">
            <template #title>
              <div class="source-title truncate-title">
                <el-icon><EditPen /></el-icon>
                Manually add items
              </div>
            </template>
            
            <ManualInput
              v-model:current-input="currentInput"
              :items="biblioItems"
              :removed-items="removedItems"
              @add-item="$emit('add-item', $event)"
              @remove-last="$emit('remove-last')"
              @undo-remove="$emit('undo-remove')"
            />
          </el-collapse-item>
        </el-collapse>

        <!-- Data Preview -->
        <DataPreview
          v-if="biblioItems.length > 0"
          :items="biblioItems"
          :current-source="currentSource"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { Download, Upload, EditPen } from '@element-plus/icons-vue'
import ZoteroImport from './ZoteroImport.vue'
import CsvUpload from './CsvUpload.vue'
import ManualInput from './ManualInput.vue'
import DataPreview from './DataPreview.vue'

const props = defineProps({
  activeDataSource: {
    type: Array,
    default: () => ['csv']
  },
  zoteroConfig: {
    type: Object,
    required: true
  },
  isZoteroLoading: {
    type: Boolean,
    default: false
  },
  fetchProgress: {
    type: Number,
    default: 0
  },
  csvHasZoteroHeader: {
    type: Boolean,
    default: true
  },
  currentInput: {
    type: Object,
    required: true
  },
  biblioItems: {
    type: Array,
    required: true
  },
  removedItems: {
    type: Array,
    required: true
  },
  currentSource: {
    type: String,
    default: ''
  }
})

const emit = defineEmits([
  'update:activeDataSource',
  'update:zoteroConfig',
  'update:csvHasZoteroHeader',
  'update:currentInput',
  'fetch-zotero',
  'csv-upload',
  'add-item',
  'remove-last',
  'undo-remove'
])
</script>

<style scoped>
.data-import-section {
  margin-bottom: 20px;
}

.el-divider--nowrap {
  text-align: center;
  white-space: nowrap;
  padding: 0;
  width: 100%;
  max-width: 100%;
}

.source-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.truncate-title {
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.center-content {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  flex-wrap: wrap;
  text-align: center;
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
}
</style>
