<template>
  <el-drawer 
    :title="title" 
    v-model="visible" 
    :direction="direction"
    :size="size"
  >
    <!-- Model Selection -->
    <el-divider class="el-divider--nowrap">Select Tag Generator</el-divider>
    <el-select 
      v-model="selectedModel" 
      placeholder="Select Tagger"
      @change="$emit('model-change', $event)"
    >
      <el-option 
        v-for="item in modelOptions" 
        :key="item.value" 
        :label="item.label" 
        :value="item.value"
      />
    </el-select>

    <!-- API Configuration -->
    <el-divider class="el-divider--nowrap">Customize API's URL</el-divider>
    <el-form label-position="top" class="api-form">
      <el-form-item label="Tagging API URL">
        <el-input 
          v-model="apiUrl" 
          placeholder="Enter API URL"
          @change="$emit('api-url-change', $event)"
        />
      </el-form-item>
      
      <el-form-item label="Tag Pool API URL">
        <el-input 
          v-model="apiAllTagsUrl" 
          placeholder="Enter API URL for getting the tag pool"
          @change="$emit('tag-pool-url-change', $event)"
        />
      </el-form-item>
    </el-form>

    <!-- Additional Settings Slot -->
    <slot name="additional-settings"></slot>

    <!-- Action Buttons -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleReset">Reset to Defaults</el-button>
        <el-button type="primary" @click="handleSave">Save Settings</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'Advanced Settings'
  },
  direction: {
    type: String,
    default: 'rtl',
    validator: (value) => ['rtl', 'ltr', 'ttb', 'btt'].includes(value)
  },
  size: {
    type: [String, Number],
    default: '30%'
  },
  selectedModel: {
    type: String,
    required: true
  },
  modelOptions: {
    type: Array,
    required: true
  },
  apiUrl: {
    type: String,
    required: true
  },
  apiAllTagsUrl: {
    type: String,
    required: true
  }
})

const emit = defineEmits([
  'update:modelValue',
  'model-change',
  'api-url-change', 
  'tag-pool-url-change',
  'reset',
  'save'
])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleReset = () => {
  emit('reset')
}

const handleSave = () => {
  emit('save')
  visible.value = false
}
</script>

<style scoped>
.el-divider--nowrap {
  text-align: center;
  white-space: nowrap;
  padding: 0;
  width: 100%;
  max-width: 100%;
}

.api-form {
  margin: 20px 0;
}

.drawer-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 16px 0;
  border-top: 1px solid var(--el-border-color-light);
}

:deep(.el-form-item__label) {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 8px;
}
</style>
