import axios from 'axios'
import { API_ENDPOINTS } from '../utils/constants.js'

/**
 * Tag Pool Service
 * Handles interactions with the tag pool API
 */
class TagPoolService {
  constructor() {
    this.apiUrl = API_ENDPOINTS.ALL_TAGS
    this.cachedTags = null
    this.isLoading = false
  }

  /**
   * Set custom API URL for tag pool
   * @param {string} url - Custom API URL
   */
  setApiUrl(url) {
    this.apiUrl = url
    // Clear cache when URL changes
    this.cachedTags = null
  }

  /**
   * Fetch all tags from the tag pool
   * @param {boolean} forceRefresh - Whether to force refresh the cache
   * @returns {Promise<Array>} - Array of tag objects
   */
  async fetchAllTags(forceRefresh = false) {
    // Return cached tags if available and not forcing refresh
    if (this.cachedTags && !forceRefresh) {
      return this.cachedTags
    }

    // Prevent multiple simultaneous requests
    if (this.isLoading) {
      // Wait for the current request to complete
      while (this.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      return this.cachedTags || []
    }

    try {
      this.isLoading = true
      const response = await axios.get(this.apiUrl)
      this.cachedTags = response.data || []
      return this.cachedTags
    } catch (error) {
      console.error('Error fetching tags from tag pool:', error)
      throw new Error('Failed to fetch tag pool. Please check your API configuration.')
    } finally {
      this.isLoading = false
    }
  }

  /**
   * Get tag names only (for filtering and suggestions)
   * @param {boolean} forceRefresh - Whether to force refresh the cache
   * @returns {Promise<Array>} - Array of tag names
   */
  async getTagNames(forceRefresh = false) {
    const tags = await this.fetchAllTags(forceRefresh)
    return tags.map(tag => tag.name || tag)
  }

  /**
   * Search tags by name
   * @param {string} query - Search query
   * @param {number} limit - Maximum number of results
   * @returns {Promise<Array>} - Array of matching tag names
   */
  async searchTags(query, limit = 40) {
    if (!query || query.length < 2) return []

    const tagNames = await this.getTagNames()
    const searchTerm = query.toLowerCase()
    
    return tagNames
      .filter(tag => tag.toLowerCase().includes(searchTerm))
      .sort((a, b) => {
        const aLower = a.toLowerCase()
        const bLower = b.toLowerCase()
        const aStartsWith = aLower.startsWith(searchTerm)
        const bStartsWith = bLower.startsWith(searchTerm)
        if (aStartsWith && !bStartsWith) return -1
        if (!aStartsWith && bStartsWith) return 1
        return aLower.indexOf(searchTerm) - bLower.indexOf(searchTerm)
      })
      .slice(0, limit)
  }

  /**
   * Find a tag object by name
   * @param {string} tagName - Name of the tag to find
   * @returns {Promise<Object|null>} - Tag object or null if not found
   */
  async findTagByName(tagName) {
    const tags = await this.fetchAllTags()
    return tags.find(tag => tag.name === tagName) || null
  }

  /**
   * Check if a tag exists in the tag pool
   * @param {string} tagName - Name of the tag to check
   * @returns {Promise<boolean>} - Whether the tag exists
   */
  async tagExists(tagName) {
    const tagNames = await this.getTagNames()
    return tagNames.includes(tagName)
  }

  /**
   * Get available metadata fields from the tag pool
   * @returns {Promise<Array>} - Array of metadata field objects
   */
  async getAvailableMetadataFields() {
    const tags = await this.fetchAllTags()
    
    if (!tags || tags.length === 0) {
      return [{ label: 'N/A', value: 'N/A' }]
    }

    const firstTag = tags[0]
    const fields = Object.keys(firstTag || {})
    const filteredFields = fields.filter(field => field !== 'name')

    return filteredFields.map(field => ({
      label: field,
      value: field
    }))
  }

  /**
   * Get tag statistics
   * @returns {Promise<Object>} - Statistics about the tag pool
   */
  async getTagStatistics() {
    const tags = await this.fetchAllTags()
    
    return {
      totalTags: tags.length,
      hasMetadata: tags.length > 0 && Object.keys(tags[0] || {}).length > 1,
      metadataFields: await this.getAvailableMetadataFields(),
      lastUpdated: new Date().toISOString()
    }
  }

  /**
   * Validate tag pool API configuration
   * @param {string} apiUrl - API URL to validate
   * @returns {Promise<boolean>} - Whether the API is accessible
   */
  async validateApiConfiguration(apiUrl) {
    try {
      const response = await axios.get(apiUrl, {
        timeout: 5000
      })
      
      // Check if response contains valid tag data
      const data = response.data
      if (Array.isArray(data) && data.length > 0) {
        // Check if tags have the expected structure
        const firstTag = data[0]
        return typeof firstTag === 'object' && (firstTag.name || typeof firstTag === 'string')
      }
      
      return Array.isArray(data) // Empty array is also valid
    } catch (error) {
      console.error('Tag pool API validation failed:', error)
      return false
    }
  }

  /**
   * Clear the tag cache
   */
  clearCache() {
    this.cachedTags = null
  }

  /**
   * Get cache status
   * @returns {Object} - Cache status information
   */
  getCacheStatus() {
    return {
      hasCachedData: this.cachedTags !== null,
      isLoading: this.isLoading,
      tagCount: this.cachedTags ? this.cachedTags.length : 0
    }
  }

  /**
   * Preload tags (useful for initialization)
   * @returns {Promise<void>}
   */
  async preloadTags() {
    try {
      await this.fetchAllTags()
    } catch (error) {
      console.warn('Failed to preload tags:', error)
      // Don't throw error for preloading failure
    }
  }
}

export default new TagPoolService()
