<template>
  <div class="page-container">
    <el-container class="main-container">
      <el-main>
        <!-- App Header -->
        <AppHeader>
          <!-- Data Import Section -->
          <DataImportSection
            v-model:active-data-source="activeDataSource"
            v-model:zotero-config="zoteroConfig"
            v-model:csv-has-zotero-header="csvHasZoteroHeader"
            v-model:current-input="currentInput"
            :biblio-items="biblioItems"
            :removed-items="removedItems"
            :current-source="currentSource"
            :is-zotero-loading="isZoteroLoading"
            :fetch-progress="fetchProgress"
            @fetch-zotero="handleFetchZotero"
            @csv-upload="handleCSVUpload"
            @add-item="handleAddItem"
            @remove-last="handleRemoveLastItem"
            @undo-remove="handleUndoRemove"
          />

          <!-- Tagging Controls -->
          <TaggingControls
            v-model:batch-size="batchSize"
            :biblio-items="biblioItems"
            :is-loading="isCSVLoading"
            :elapsed-time="elapsedCSVTime"
            :suggested-batch-size="suggestedBatchSize"
            @submit="handleSubmitBiblioItems"
            @clear-all="handleClearAllItems"
          />

          <!-- Loading Indicator -->
          <LoadingSpinner
            v-if="isLoading || isCSVLoading"
            :loading="true"
            :message="isCSVLoading ? `Processing items (${elapsedCSVTime}s)...` : 'Generating tags...'"
            :elapsed-time="isCSVLoading ? elapsedCSVTime : elapsedTime"
            :show-timer="true"
            message="Generating tags... This may take a few minutes."
          />


          <!-- Results Section -->
          <ResultsSection
            v-if="results.length > 0"
            :results="results"
            :all-indexed-biblio-items="allIndexedBiblioItems"
            :deselected-tags="deselectedTags"
            :new-tags="newTags"
            :all-tag-candidates="allTagCandidates"
            :has-loaded-tags="hasLoadedTags"
            :is-fetching-tags="isFetchingTags"
            :screen-is-portrait="screenIsPortrait"
            @toggle-tag="handleToggleTag"
            @add-new-tag="handleAddNewTag"
            @remove-new-tag="handleRemoveNewTag"
            @drag-start="handleDragStart"
            @drop="handleDrop"
          />
          <!-- Export Section -->
          <ExportSection
            v-if="results.length > 0"
            :results="results"
            :all-indexed-biblio-items="allIndexedBiblioItems"
            :new-tag-mark-enabled="newTagMarkEnabled"
            :new-tag-mark-position="newTagMarkPosition"
            :new-tag-mark="newTagMark"
            :metadata-suffix-enabled="metadataSuffixEnabled"
            :selected-metadata-fields="selectedMetadataFields"
            :available-metadata-fields="availableMetadataFields"
            :custom-suffix-enabled="customSuffixEnabled"
            :custom-suffix="customSuffix"
            :processed-tag-enabled="processedTagEnabled"
            :processed-tag="processedTag"
            :screen-is-portrait="screenIsPortrait"
            :is-zotero-config-valid="isZoteroConfigValid"
            :current-source="currentSource"
            @download-csv="handleDownloadCSV"
            @save-to-zotero="handleSaveToZotero"
          />
        </AppHeader>

        <!-- Floating Action Buttons -->
        <FloatingButtons
          :show-expand-toggle="results.length > 0"
          :all-expanded="activeNames.length === paginatedResults.length"
          @toggle-all="handleToggleAllResults"
          @open-settings="drawerVisible = true"
        />

        <!-- Advanced Settings Drawer -->
        <AdvancedSettings
          v-model="drawerVisible"
          :direction="drawerDirection"
          :selected-model="selectedModel"
          :model-options="modelOptions"
          :api-url="apiUrl"
          :api-all-tags-url="apiAllTagsUrl"
          @model-change="handleModelChange"
          @api-url-change="handleApiUrlChange"
          @tag-pool-url-change="handleTagPoolUrlChange"
          @reset="handleResetSettings"
          @save="handleSaveSettings"
        />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Import components
import AppHeader from './components/layout/AppHeader.vue'
import FloatingButtons from './components/layout/FloatingButtons.vue'
import AdvancedSettings from './components/layout/AdvancedSettings.vue'
import DataImportSection from './components/import/DataImportSection.vue'
import TaggingControls from './components/tagging/TaggingControls.vue'
import LoadingSpinner from './components/shared/LoadingSpinner.vue'
import ResultsSection from './components/results/ResultsSection.vue'
import ExportSection from './components/export/ExportSection.vue'

// Import composables
import { useAppState } from './composables/useAppState.js'
import { useZoteroApi } from './composables/useZoteroApi.js'
import { useTagging } from './composables/useTagging.js'
import { useTagManagement } from './composables/useTagManagement.js'
import { usePagination } from './composables/usePagination.js'
import { useExport } from './composables/useExport.js'

// Import utilities
import { parseCSVFile } from './utils/csvUtils.js'
import { BATCH_CONFIG } from './utils/constants.js'

// Initialize composables
const appState = useAppState()
const zoteroApi = useZoteroApi()
const tagging = useTagging()
const tagManagement = useTagManagement()
const pagination = usePagination(computed(() => appState.results))
const exportFunctionality = useExport()

// Destructure commonly used state
const {
  // UI State
  drawerVisible,
  screenIsPortrait,
  drawerDirection,
  activeDataSource,

  // Data State
  biblioItems,
  allIndexedBiblioItems,
  results,
  removedItems,
  currentSource,

  // Loading States
  isLoading,
  isCSVLoading,
  isZoteroLoading,
  isFetchingTags,

  // Timer States
  elapsedTime,
  elapsedCSVTime,
  fetchProgress,

  // API Configuration
  apiUrl,
  apiAllTagsUrl,
  selectedModel,
  modelOptions,

  // Export Configuration
  newTagMarkEnabled,
  newTagMarkPosition,
  newTagMark,
  metadataSuffixEnabled,
  selectedMetadataFields,
  customSuffixEnabled,
  customSuffix,
  processedTagEnabled,
  processedTag,

  // Methods
  clearAllData,
  setApiUrls,
  updateScreenOrientation
} = appState

// Destructure Zotero functionality
const {
  zoteroConfig,
  isZoteroConfigValid,
  fetchZoteroItems,
  saveTagsToZotero
} = zoteroApi

// Destructure tagging functionality
const {
  batchSize,
  submitBiblioItems,
  resetTaggingState
} = tagging

// Destructure tag management
const {
  deselectedTags,
  newTags,
  inputNewTagValue,
  allTagCandidates,
  hasLoadedTags,
  availableMetadataFields,
  fetchAllTags,
  isTagDeselected,
  toggleTag,
  addNewTag,
  removeNewTag,
  handleDragStart,
  handleDragOver,
  handleDrop
} = tagManagement

// Destructure pagination
const {
  currentPage,
  pageSize,
  activeNames,
  paginatedResults,
  handleSizeChange,
  handleCurrentChange,
  toggleAllItems
} = pagination

// Additional state
const currentInput = ref({ title: '', abstract: '' })
const csvHasZoteroHeader = ref(true)

// Computed properties
const suggestedBatchSize = computed(() => {
  return BATCH_CONFIG.calculateSuggestedBatchSize(biblioItems.value.length)
})

// Event Handlers
const handleFetchZotero = async () => {
  const items = await fetchZoteroItems()
  if (items.length > 0) {
    biblioItems.value = items
    removedItems.value = []
    currentSource.value = 'Zotero'
  }
}

const handleCSVUpload = async (file) => {
  try {
    const items = await parseCSVFile(file, csvHasZoteroHeader.value)
    biblioItems.value = items
    removedItems.value = []
    currentSource.value = 'CSV'
    ElMessage.success(`Successfully imported ${items.length} items from CSV`)
  } catch (error) {
    console.error('Error parsing CSV:', error)
    ElMessage.error('Failed to parse CSV file')
  }
}

const handleAddItem = () => {
  if (currentInput.value.title.trim() || currentInput.value.abstract.trim()) {
    biblioItems.value.push({
      title: currentInput.value.title.trim(),
      abstract: currentInput.value.abstract.trim()
    })
    currentInput.value = { title: '', abstract: '' }
    currentSource.value = 'Manual'
    ElMessage.success('Item added successfully!')
  }
}

const handleRemoveLastItem = () => {
  if (biblioItems.value.length > 0) {
    const removedItem = biblioItems.value.pop()
    removedItems.value.push(removedItem)
    ElMessage.success('Last item removed. Click Undo to restore.')
  }
}

const handleUndoRemove = () => {
  if (removedItems.value.length > 0) {
    const itemToRestore = removedItems.value.pop()
    biblioItems.value.push(itemToRestore)
    ElMessage.success('Item restored successfully!')
  }
}

const handleSubmitBiblioItems = async () => {
  const result = await submitBiblioItems(biblioItems.value, selectedModel.value, apiUrl.value)
  if (result.results.length > 0) {
    results.value = result.results
    allIndexedBiblioItems.value = result.allIndexedBiblioItems
    await fetchAllTags() // Load tags for tag management
  }
}

const handleClearAllItems = () => {
  ElMessageBox.confirm('This will remove all items. Continue?', 'Warning', {
    confirmButtonText: 'Yes',
    cancelButtonText: 'No',
    type: 'warning',
  }).then(() => {
    clearAllData()
    resetTaggingState()
    tagManagement.clearTagManagementState()
    pagination.resetPagination()
    ElMessage.success('All items have been cleared')
  }).catch(() => {
    // User cancelled
  })
}

const handleToggleAllResults = () => {
  toggleAllItems()
}

// Tag management handlers
const handleToggleTag = (resultIndex, tag) => {
  toggleTag(resultIndex, tag)
}

const handleAddNewTag = (resultIndex, tagText, matchedTags) => {
  return addNewTag(resultIndex, tagText, matchedTags)
}

const handleRemoveNewTag = (resultIndex, tagText) => {
  removeNewTag(resultIndex, tagText)
}

// Export handlers
const handleDownloadCSV = () => {
  const tagOptions = {
    isTagDeselected,
    newTags,
    allTagCandidates,
    metadataSuffixEnabled,
    selectedMetadataFields,
    customSuffixEnabled,
    customSuffix,
    newTagMarkEnabled,
    newTagMarkPosition,
    newTagMark,
    processedTagEnabled,
    processedTag
  }
  exportFunctionality.downloadCSVData(results.value, allIndexedBiblioItems.value, tagOptions)
}

const handleSaveToZotero = async () => {
  const tagOptions = {
    isTagDeselected,
    newTags,
    allTagCandidates,
    metadataSuffixEnabled,
    selectedMetadataFields,
    customSuffixEnabled,
    customSuffix,
    newTagMarkEnabled,
    newTagMarkPosition,
    newTagMark,
    processedTagEnabled,
    processedTag
  }
  await saveTagsToZotero(results.value, allIndexedBiblioItems.value, tagOptions)
}

// Settings handlers
const handleModelChange = (model) => {
  selectedModel.value = model
}

const handleApiUrlChange = (url) => {
  apiUrl.value = url
}

const handleTagPoolUrlChange = (url) => {
  apiAllTagsUrl.value = url
}

const handleResetSettings = () => {
  setApiUrls('https://tagger.yizehu.com/api/generate_tags', 'https://tagger.yizehu.com/api/tags/all')
  selectedModel.value = 'gpt-4o-mini'
  ElMessage.success('Settings reset to defaults')
}

const handleSaveSettings = () => {
  ElMessage.success('Settings saved successfully')
}

// Lifecycle
onMounted(() => {
  window.addEventListener('resize', updateScreenOrientation)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenOrientation)
  tagging.cleanup()
})

// Add new function to fetch Zotero items
const fetchZoteroItems = async () => {
  if (!isZoteroConfigValid.value) return

  try {
    isZoteroLoading.value = true
    const { libraryId, apiKey, tag } = zoteroConfig.value

    // Initialize base URL for group library
    zoteroBaseUrl.value = `https://api.zotero.org/groups/${libraryId}`;
    let isGroupLibrary = true;

    // Test if the `libraryId` belongs to a group or personal library
    let testResponse;
    try {
      // Test request for group library
      testResponse = await axios.get(`${zoteroBaseUrl.value}/items`, {
        params: {
          tag,
          limit: 1,
          includeTrashed: 0
        },
        headers: {
          'Zotero-API-Version': '3',
          'Authorization': `Bearer ${apiKey}`
        }
      });

      // If the response returns an empty list, switch to personal library
      if (Array.isArray(testResponse.data) && testResponse.data.length === 0) {
        // Switch to personal library URL
        zoteroBaseUrl.value = `https://api.zotero.org/users/${libraryId}`;
        isGroupLibrary = false;

        // Test request for personal library
        testResponse = await axios.get(`${zoteroBaseUrl.value}/items`, {
          params: {
            tag,
            limit: 1,
            includeTrashed: 0
          },
          headers: {
            'Zotero-API-Version': '3',
            'Authorization': `Bearer ${apiKey}`
          }
        });

        // If the personal library is also empty, handle appropriately
        if (Array.isArray(testResponse.data) && testResponse.data.length === 0) {
          throw new Error('Both group and personal libraries returned empty lists.');
        }
      }
    } catch (error) {
      // Handle unexpected errors during the test request
      if ((Array.isArray(testResponse.data) && testResponse.data.length === 0) && error.response && error.response.status === 403) {
        ElMessage.error('No item with the given tag found. Please make sure all items are synced to the online library, and check your library ID and API key permissions. (Empty or 403 Forbidden)');
        return;
      }
      ElMessage.error('Failed to connect to Zotero library. Please check your configuration.');
      return;
    }

    // First, get total items count
    const countResponse = await axios.get(
      `${zoteroBaseUrl.value}/items`,
      {
        params: {
          tag,
          limit: 1,
          includeTrashed: 0
        },
        headers: {
          'Zotero-API-Version': '3',
          'Authorization': `Bearer ${apiKey}`
        }
      }
    )

    const totalItems = parseInt(countResponse.headers['total-results'])
    const numberOfRequests = Math.ceil(totalItems / MAX_ITEMS_PER_REQUEST)
    let allFetchedItems = []

    // Fetch all items in parallel
    const requests = Array.from({ length: numberOfRequests }, (_, index) => {
      return axios.get(
        `${zoteroBaseUrl.value}/items`,
        {
          params: {
            tag,
            limit: MAX_ITEMS_PER_REQUEST,
            start: index * MAX_ITEMS_PER_REQUEST,
            includeTrashed: 0,
            itemType: '-attachment || note' // Exclude attachments and notes
          },
          headers: {
            'Zotero-API-Version': '3',
            'Authorization': `Bearer ${apiKey}`
          }
        }
      )
    })

    const responses = await Promise.all(requests)

    // Process all responses
    responses.forEach(response => {
      const items = response.data.map(item => ({
        key: item.key,
        version: item.version,
        title: item.data.title || '',
        abstract: item.data.abstractNote || '',
        creators: item.data.creators || [],
        itemType: item.data.itemType || '',
        date: item.data.date || '',
        publicationTitle: item.data.publicationTitle || '',
        volume: item.data.volume || '',
        issue: item.data.issue || '',
        pages: item.data.pages || '',
        url: item.data.url || '',
        extra: item.data.extra || '',
      })).filter(item => item.title || item.abstract)

      allFetchedItems = [...allFetchedItems, ...items]
    })

    // Update the items list
    biblioItems.value = allFetchedItems
    removedItems.value = [] // Clear removed items history
    currentSource.value = 'Zotero'

    ElMessage.success(`Successfully fetched ${allFetchedItems.length} items from Zotero`)

    // // Clear sensitive data
    // zoteroConfig.value = {
    //   libraryId: '',
    //   apiKey: '',
    //   tag: ''
    // }

  } catch (error) {
    console.error('Error fetching Zotero items:', error)
    let errorMessage = 'Failed to fetch items from Zotero'

    if (error.response) {
      switch (error.response.status) {
        case 403:
          errorMessage = 'Invalid API key or insufficient permissions'
          break
        case 404:
          errorMessage = 'Library not found. Please check your Library ID'
          break
        case 429:
          errorMessage = 'Too many requests. Please try again later'
          break
        default:
          errorMessage = error.response.data?.message || errorMessage
      }
    }

    ElMessage.error(errorMessage)
  } finally {
    isZoteroLoading.value = false
  }
}

// Add progress tracking (optional)
const fetchProgress = ref(0)
const updateProgress = (current, total) => {
  fetchProgress.value = Math.round((current / total) * 100)
}

const startTimer = (isCSV = false) => {
  if (isCSV) {
    elapsedCSVTime.value = 0
    csvTimerInterval = setInterval(() => {
      elapsedCSVTime.value++
    }, 1000)
  } else {
    elapsedTime.value = 0
    timerInterval = setInterval(() => {
      elapsedTime.value++
    }, 1000)
  }
}

const stopTimer = (isCSV = false) => {
  if (isCSV) {
    if (csvTimerInterval) {
      clearInterval(csvTimerInterval)
      csvTimerInterval = null
    }
  } else {
    if (timerInterval) {
      clearInterval(timerInterval)
      timerInterval = null
    }
  }
}

const submitBiblioItems = async () => {
  try {
    isCSVLoading.value = true
    startTimer(true)

    // Clear previous results and collapse states
    results.value = []
    activeNames.value = []

    // VERY IMPORTANT: Index all items. After this step, allIndexedBiblioItems should be used instead of biblioItems.
    allIndexedBiblioItems.value = biblioItems.value.map((item, idx) => ({
      ...item,
      index: idx,
    }))

    // Use the user's chosen batch size (or suggested if not set)
    const size = batchSize.value || suggestedBatchSize.value

    // Process items in batches
    for (let i = 0; i < allIndexedBiblioItems.value.length; i += size) {
      const batch = allIndexedBiblioItems.value.slice(i, i + size)
      const response = await axios.post(`${apiUrl.value}`, {
        model: selectedModel.value,
        items: batch.map(article => ({
          key: article.key,
          title: article.title,
          abstract: article.abstract,
          index: article.index
        }))
      })

      results.value.push(...response.data)
      ElMessage.success(`Batch ${Math.floor(i / size) + 1} tagged successfully!`)
      await fetchAllTags()
    }
  } catch (error) {
    console.error('Error batch-tagging articles:', error)
    ElMessage.error('An error occurred while batch-tagging articles.')
  } finally {
    stopTimer(true)
    isCSVLoading.value = false
  }
}

const clearAllItems = () => {
  ElMessageBox.confirm('This will remove all items. Continue?', 'Warning', {
    confirmButtonText: 'Yes',
    cancelButtonText: 'No',
    type: 'warning',
  }).then(() => {
    biblioItems.value = []
    allIndexedBiblioItems.value = []
    results.value = []
    removedItems.value = []
    currentSource.value = ''
    batchSize.value = null
    suggestedBatchSize.value = null
    deselectedTags.value = new Set()
    newTags.value = new Map()
    // allTagCandidates.value = []
    ElMessage({
      type: 'success',
      message: 'All items have been cleared',
      duration: 2000
    })
  }).catch((error) => {
    console.error('Error in clearAllItems:', error)
  })
}

const appendItem = () => {
  if (currentInput.value.title.trim() || currentInput.value.abstract.trim()) {
    biblioItems.value.push({
      title: currentInput.value.title.trim(),
      abstract: currentInput.value.abstract.trim()
    })
    // Clear input fields after adding
    currentInput.value = { title: '', abstract: '' }
    ElMessage.success('Item added successfully!')
  }
}

const removeLastItem = () => {
  if (biblioItems.value.length > 0) {
    const removedItem = biblioItems.value.pop()
    removedItems.value.push(removedItem)
    ElMessage.success('Last item removed. Click Undo to restore.')
  }
}

const undoRemove = () => {
  if (removedItems.value.length > 0) {
    const itemToRestore = removedItems.value.pop()
    biblioItems.value.push(itemToRestore)
    ElMessage.success('Item restored successfully!')
  }
}

const handleFormSubmit = (event) => {
  event.preventDefault() // Prevent default form submission
  appendItem() // Call appendItem function
}


// Handle CSV file upload
const handleCSVUpload = (file, hasHeader = csvHasZoteroHeader.value) => {
  Papa.parse(file, {
    complete: (result) => {
      let data;
      if (hasHeader) {
        // If the CSV has a header, use named fields
        data = result.data
          .filter(row => row.Key) // Ensure the row has a Key (or any required field)
          .map((row) => ({
            key: row.Key?.replace(/^"|"$/g, '').trim() || '',
            itemType: row['Item Type']?.replace(/^"|"$/g, '').trim() || '',
            publicationYear: row['Publication Year']?.replace(/^"|"$/g, '').trim() || '',
            author: row.Author?.replace(/^"|"$/g, '').trim() || '',
            title: row.Title?.replace(/^"|"$/g, '').trim() || '',
            publicationTitle: row['Publication Title']?.replace(/^"|"$/g, '').trim() || '',
            isbn: row.ISBN?.replace(/^"|"$/g, '').trim() || '',
            issn: row.ISSN?.replace(/^"|"$/g, '').trim() || '',
            doi: row.DOI?.replace(/^"|"$/g, '').trim() || '',
            url: row.Url?.replace(/^"|"$/g, '').trim() || '',
            abstract: row['Abstract Note']?.replace(/^"|"$/g, '').trim() || '',
            date: row.Date?.replace(/^"|"$/g, '').trim() || '',
            dateAdded: row['Date Added']?.replace(/^"|"$/g, '').trim() || '',
            dateModified: row['Date Modified']?.replace(/^"|"$/g, '').trim() || '',
            accessDate: row['Access Date']?.replace(/^"|"$/g, '').trim() || '',
            pages: row.Pages?.replace(/^"|"$/g, '').trim() || '',
            numPages: row['Num Pages']?.replace(/^"|"$/g, '').trim() || '',
            issue: row.Issue?.replace(/^"|"$/g, '').trim() || '',
            volume: row.Volume?.replace(/^"|"$/g, '').trim() || '',
            numberOfVolumes: row['Number Of Volumes']?.replace(/^"|"$/g, '').trim() || '',
            journalAbbreviation: row['Journal Abbreviation']?.replace(/^"|"$/g, '').trim() || '',
            shortTitle: row['Short Title']?.replace(/^"|"$/g, '').trim() || '',
            series: row.Series?.replace(/^"|"$/g, '').trim() || '',
            seriesNumber: row['Series Number']?.replace(/^"|"$/g, '').trim() || '',
            seriesText: row['Series Text']?.replace(/^"|"$/g, '').trim() || '',
            seriesTitle: row['Series Title']?.replace(/^"|"$/g, '').trim() || '',
            publisher: row.Publisher?.replace(/^"|"$/g, '').trim() || '',
            place: row.Place?.replace(/^"|"$/g, '').trim() || '',
            language: row.Language?.replace(/^"|"$/g, '').trim() || '',
            rights: row.Rights?.replace(/^"|"$/g, '').trim() || '',
            type: row.Type?.replace(/^"|"$/g, '').trim() || '',
            archive: row.Archive?.replace(/^"|"$/g, '').trim() || '',
            archiveLocation: row['Archive Location']?.replace(/^"|"$/g, '').trim() || '',
            libraryCatalog: row['Library Catalog']?.replace(/^"|"$/g, '').trim() || '',
            callNumber: row['Call Number']?.replace(/^"|"$/g, '').trim() || '',
            extra: row.Extra?.replace(/^"|"$/g, '').trim() || '',
            notes: row.Notes?.replace(/^"|"$/g, '').trim() || '',
            fileAttachments: row['File Attachments']?.replace(/^"|"$/g, '').trim() || '',
            linkAttachments: row['Link Attachments']?.replace(/^"|"$/g, '').trim() || '',
            editor: row.Editor?.replace(/^"|"$/g, '').trim() || '',
            seriesEditor: row['Series Editor']?.replace(/^"|"$/g, '').trim() || '',
            translator: row.Translator?.replace(/^"|"$/g, '').trim() || '',
            contributor: row.Contributor?.replace(/^"|"$/g, '').trim() || '',
            attorneyAgent: row['Attorney Agent']?.replace(/^"|"$/g, '').trim() || '',
            bookAuthor: row['Book Author']?.replace(/^"|"$/g, '').trim() || '',
            castMember: row['Cast Member']?.replace(/^"|"$/g, '').trim() || '',
            commenter: row.Commenter?.replace(/^"|"$/g, '').trim() || '',
            composer: row.Composer?.replace(/^"|"$/g, '').trim() || '',
            cosponsor: row.Cosponsor?.replace(/^"|"$/g, '').trim() || '',
            counsel: row.Counsel?.replace(/^"|"$/g, '').trim() || '',
            interviewer: row.Interviewer?.replace(/^"|"$/g, '').trim() || '',
            producer: row.Producer?.replace(/^"|"$/g, '').trim() || '',
            recipient: row.Recipient?.replace(/^"|"$/g, '').trim() || '',
            reviewedAuthor: row['Reviewed Author']?.replace(/^"|"$/g, '').trim() || '',
            scriptwriter: row.Scriptwriter?.replace(/^"|"$/g, '').trim() || '',
            wordsBy: row['Words By']?.replace(/^"|"$/g, '').trim() || '',
            guest: row.Guest?.replace(/^"|"$/g, '').trim() || '',
            number: row.Number?.replace(/^"|"$/g, '').trim() || '',
            edition: row.Edition?.replace(/^"|"$/g, '').trim() || '',
            runningTime: row['Running Time']?.replace(/^"|"$/g, '').trim() || '',
            scale: row.Scale?.replace(/^"|"$/g, '').trim() || '',
            medium: row.Medium?.replace(/^"|"$/g, '').trim() || '',
            artworkSize: row['Artwork Size']?.replace(/^"|"$/g, '').trim() || '',
            filingDate: row['Filing Date']?.replace(/^"|"$/g, '').trim() || '',
            applicationNumber: row['Application Number']?.replace(/^"|"$/g, '').trim() || '',
            assignee: row.Assignee?.replace(/^"|"$/g, '').trim() || '',
            issuingAuthority: row['Issuing Authority']?.replace(/^"|"$/g, '').trim() || '',
            country: row.Country?.replace(/^"|"$/g, '').trim() || '',
            meetingName: row['Meeting Name']?.replace(/^"|"$/g, '').trim() || '',
            conferenceName: row['Conference Name']?.replace(/^"|"$/g, '').trim() || '',
            court: row.Court?.replace(/^"|"$/g, '').trim() || '',
            references: row.References?.replace(/^"|"$/g, '').trim() || '',
            reporter: row.Reporter?.replace(/^"|"$/g, '').trim() || '',
            legalStatus: row['Legal Status']?.replace(/^"|"$/g, '').trim() || '',
            priorityNumbers: row['Priority Numbers']?.replace(/^"|"$/g, '').trim() || '',
            programmingLanguage: row['Programming Language']?.replace(/^"|"$/g, '').trim() || '',
            version: row.Version?.replace(/^"|"$/g, '').trim() || '',
            system: row.System?.replace(/^"|"$/g, '').trim() || '',
            code: row.Code?.replace(/^"|"$/g, '').trim() || '',
            codeNumber: row['Code Number']?.replace(/^"|"$/g, '').trim() || '',
            section: row.Section?.replace(/^"|"$/g, '').trim() || '',
            session: row.Session?.replace(/^"|"$/g, '').trim() || '',
            committee: row.Committee?.replace(/^"|"$/g, '').trim() || '',
            history: row.History?.replace(/^"|"$/g, '').trim() || '',
            legislativeBody: row['Legislative Body']?.replace(/^"|"$/g, '').trim() || ''
          }))
          .filter(item => item.title !== '' || item.abstractNote !== ''); // Filter out empty items
      } else {
        // If the CSV does not have a header, use positional indexing
        data = result.data
          .filter(row => row.length >= 2) // Ensure the row has at least two columns
          .map((row) => ({
            title: row[0]?.replace(/^"|"$/g, '').trim() || '',
            abstract: row[1]?.replace(/^"|"$/g, '').trim() || ''
          }))
          .filter(item => item.title !== '' || item.abstract !== ''); // Filter out empty items
      }

      batchSize.value = null;
      biblioItems.value = data;
      removedItems.value = []; // Clear removed items history when new CSV is uploaded
      currentSource.value = 'local file';
      ElMessage.success(`CSV file processed: ${data.length} articles found`);
    },
    header: hasHeader, // Use the first row as headers only if hasHeader is true
    skipEmptyLines: true
  });
  return false;
};

// Tag management functions
const toggleTag = (resultIndex, tag) => {
  const key = `${resultIndex}-${tag}`
  if (deselectedTags.value.has(key)) {
    deselectedTags.value.delete(key)
  } else {
    deselectedTags.value.add(key)
  }
}

const isTagDeselected = (resultIndex, tag) => {
  return deselectedTags.value.has(`${resultIndex}-${tag}`)
}

const addNewTag = (resultIndex, event) => {
  const tag = inputNewTagValue.value.trim()
  if (!tag) return

  if (!newTags.value.has(resultIndex)) {
    newTags.value.set(resultIndex, new Set())
  }

  // Check if the tag is already in the Set for this resultIndex
  const existingTags = newTags.value.get(resultIndex);
  const isTagAlreadyAdded = Array.from(existingTags).some(
    (existingTag) => existingTag.text === tag
  );

  if (isTagAlreadyAdded || (results.value[resultIndex].tags.matched_tags.includes(tag) && !isTagDeselected(resultIndex, tag))) {
    ElMessage.warning('Tag already exists in matched tags.')
    return; // If the tag already exists, do not add it again
  }

  // Check if tag exists in tagNames
  const isExistingTag = tagNames.value.includes(tag)

  newTags.value.get(resultIndex).add({
    text: tag,
    isMatched: isExistingTag
  })
  inputNewTagValue.value = ''
  searchQuery.value.set(resultIndex, '')
  showTagSuggestions.value.set(resultIndex, false)
}

const addNewTagDoubleClick = (resultIndex, tag) => {
  if (!tag) return; // Guard clause for empty tags

  if (!newTags.value.has(resultIndex)) {
    newTags.value.set(resultIndex, new Set());
  }

  // Check if the tag is already in the Set for this resultIndex
  const existingTags = newTags.value.get(resultIndex);
  const isTagAlreadyAdded = Array.from(existingTags).some(
    (existingTag) => existingTag.text === tag
  );

  if (isTagAlreadyAdded || (results.value[resultIndex].tags.matched_tags.includes(tag) && !isTagDeselected(resultIndex, tag))) {
    ElMessage.warning('Tag already exists in matched tags.')
    return; // If the tag already exists, do not add it again
  }

  // Check if tag exists in tagNames
  const isExistingTag = tagNames.value.includes(tag);

  // Add the tag to the newTags Set
  newTags.value.get(resultIndex).add({
    text: tag,
    isMatched: isExistingTag
  })
  ElMessage.success('New tag added to matched tags!')
}

const removeNewTag = (resultIndex, tagText) => {
  const tags = newTags.value.get(resultIndex)
  if (tags) {
    for (const tag of tags) {
      if (tag.text === tagText) {
        tags.delete(tag)
        break
      }
    }
  }
}

// Add new function to handle drag start
const handleDragStart = (event, tag, sourceSection) => {
  event.dataTransfer.setData('text/plain', JSON.stringify({
    tag,
    sourceSection
  }))
  fetchAllTags()
}

// Add new function to handle drag over
const handleDragOver = (event) => {
  event.preventDefault()
}

// Add new function to handle drop
const handleDrop = (event, resultIndex) => {
  event.preventDefault()
  const data = JSON.parse(event.dataTransfer.getData('text/plain'))
  const { tag, sourceSection } = data

  // Add to matched tags if not already present
  if (!results.value[resultIndex].tags.matched_tags.includes(tag) || isTagDeselected(resultIndex, tag)) {
    if (!newTags.value.has(resultIndex)) {
      newTags.value.set(resultIndex, new Set())
    }

    // Check if the tag is already in the Set for this resultIndex
    const existingTags = newTags.value.get(resultIndex);
    const isTagAlreadyAdded = Array.from(existingTags).some(
      (existingTag) => existingTag.text === tag
    );

    if (isTagAlreadyAdded) {
      ElMessage.warning('Tag already exists in matched tags.')
      return; // If the tag already exists, do not add it again
    }

    const isExistingTag = tagNames.value.includes(tag)

    newTags.value.get(resultIndex).add({
      text: tag,
      isMatched: isExistingTag,
    })
    // ElMessage.success(`Added "${tag}" to Matched Tags`)
  } else {
    ElMessage.warning('Tag already exists in matched tags.');
    return;
  }
}

// Function to fetch all tags
const fetchAllTags = async () => {
  if (hasLoadedTags.value) return // Don't fetch if already loaded

  try {
    isFetchingTags.value = true
    const response = await axios.get(`${apiAllTagsUrl.value}`)
    allTagCandidates.value = response.data
    hasLoadedTags.value = true
    ElMessage.success('Tag pool loaded successfully.')
  } catch (error) {
    console.error('Error fetching tags:', error)
    ElMessage.error('Failed to fetch tags')
    allTagCandidates.value = []
  } finally {
    isFetchingTags.value = false
  }
}

// Watch for changes to metadataSuffixEnabled
watch(metadataSuffixEnabled, async (newValue) => {
  if (newValue === true) {
    await fetchAllTags();
  }
});

// Debounced filter function for smooth typing experience
const debouncedFilter = debounce((query, resultIndex) => {
  if (!query || query.length < 2) {
    showTagSuggestions.value.set(resultIndex, false)
    return
  }
  showTagSuggestions.value.set(resultIndex, true)
}, 300)

// Get filtered tags based on search query
const getFilteredTags = (query) => {
  if (!query || query.length < 2) return []

  const searchTerm = query.toLowerCase()
  return tagNames.value
    .filter(tag => tag.toLowerCase().includes(searchTerm))
    .sort((a, b) => {
      const aLower = a.toLowerCase()
      const bLower = b.toLowerCase()
      const aStartsWith = aLower.startsWith(searchTerm)
      const bStartsWith = bLower.startsWith(searchTerm)
      if (aStartsWith && !bStartsWith) return -1
      if (!aStartsWith && bStartsWith) return 1
      return aLower.indexOf(searchTerm) - bLower.indexOf(searchTerm)
    })
    .slice(0, 40) // Limit to first 20 matches
}

// Handle input focus
const handleInputFocus = async (resultIndex) => {
  if (!hasLoadedTags.value) {
    await fetchAllTags()
  }
  showTagSuggestions.value.set(resultIndex, true)
}

// Handle input changes
const handleSearchInput = (value, resultIndex) => {
  searchQuery.value.set(resultIndex, value) // Update search query for filtering
  debouncedFilter(value, resultIndex)
}

// Handle tag selection
const selectTag = (resultIndex, tag) => {
  if (!newTags.value.has(resultIndex)) {
    newTags.value.set(resultIndex, new Set())
  }

  // Check if the tag is already in the Set for this resultIndex
  const existingTags = newTags.value.get(resultIndex);
  const isTagAlreadyAdded = Array.from(existingTags).some(
    (existingTag) => existingTag.text === tag
  );

  if (isTagAlreadyAdded || (results.value[resultIndex].tags.matched_tags.includes(tag) && !isTagDeselected(resultIndex, tag))) {
    ElMessage.warning('Tag already exists in matched tags.')
    return; // If the tag already exists, do not add it again
  }

  newTags.value.get(resultIndex).add({
    text: tag,
    isMatched: true
  })
  inputNewTagValue.value = ''
  searchQuery.value.set(resultIndex, '')
  showTagSuggestions.value.set(resultIndex, false)
}

// Close suggestions dropdown
const closeSuggestions = (resultIndex) => {
  setTimeout(() => {
    showTagSuggestions.value.set(resultIndex, false)
  }, 200)
}

// Pagination
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1 // Reset to first page when changing page size
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

const toggleAllResults = () => {
  if (activeNames.value.length === paginatedResults.value.length) {
    activeNames.value = []
  } else {
    activeNames.value = paginatedResults.value.map(result => result.index)
  }
}

// Function to get abstract by index
const getAbstractByIndex = (index) => {
  const item = allIndexedBiblioItems.value.find(item => item.index === index)
  return item ? item.abstract : 'Abstract not found'
}

// Download CSV
const exportFields = ref([])
const includeConceptTags = ref(true)
const includePersonOrgTags = ref(true)
const includeTimePlaceTags = ref(true)
const showExportDialog = ref(false)
const selectAll = ref(false)

const exportOptions = ref({
  fieldDelimiter: ',',
  tagDelimiter: ';',
  valueMarkers: '"'
})

const availableFields = computed(() => {
  if (allIndexedBiblioItems.value.length === 0) return []

  return Object.keys(allIndexedBiblioItems.value[0])
    .filter(key => key !== 'index')
    .filter(key => allIndexedBiblioItems.value.some(item => {
      const value = item[key]
      return value !== null && value !== undefined && String(value).trim() !== ''
    }))
})

const openExportDialog = () => {
  exportFields.value = [...availableFields.value]
  selectAll.value = true
  showExportDialog.value = true
}

const handleSelectAll = (val) => {
  if (val) {
    exportFields.value = [...availableFields.value]
  } else {
    exportFields.value = []
  }
}

const handleExport = () => {
  downloadCSV()
  showExportDialog.value = false
}

const downloadCSV = () => {
  // If the user directly clicked the export button, the exportFields array would be empty. So it should be set to all available fields by default.
  if (exportFields.value.length === 0) {
    exportFields.value = [...availableFields.value];
  }

  const { fieldDelimiter, tagDelimiter, valueMarkers } = exportOptions.value;
  const startMarker = valueMarkers || '';
  const endMarker = valueMarkers || startMarker;

  const csvData = results.value.map(result => {
    const item = allIndexedBiblioItems.value.find(item => item.index === result.index);
    const row = exportFields.value.map(field => {
      // Replace undefined or null field values with an empty string
      let fieldValue = item[field] != null ? item[field] : '';

      // If fieldValue is not a string and is an object or array (e.g. a list of several authors), expand it
      if (isReactive(fieldValue) || Array.isArray(fieldValue)) {
        const rawFieldValue = toRaw(fieldValue); // Convert to raw object/array

        if (Array.isArray(rawFieldValue)) {
          // Check for array of objects with firstName or lastName
          fieldValue = rawFieldValue
            .map(entry => {
              if (entry.lastName || entry.firstName) {
                // Use empty string if one of the values is missing
                const lastName = entry.lastName || '';
                const firstName = entry.firstName || '';
                return `${lastName},${firstName}`.trim();
              }
              return ''; // Skip invalid entries
            })
            .filter(Boolean) // Remove empty strings
            .join(tagDelimiter); // Join with tagDelimiter
        }
      } else if (typeof fieldValue === 'object') {
        // Handle plain objects by joining values with tagDelimiter
        fieldValue = Object.values(fieldValue).join(tagDelimiter);
      } else if (typeof fieldValue !== 'string') {
        // Convert other non-string values to strings
        fieldValue = String(fieldValue);
      }
      return `${startMarker}${fieldValue}${endMarker}`;
    });

    // Decorate active matched tags (those matched tags that are still active (not de-selected) after being edited by the user)
    const decoratedActiveMatchedTags = result.tags.matched_tags
      .filter(tag => !isTagDeselected(result.index, tag))
      .map(tagText => {
        const matchingTag = allTagCandidates.value.find(t => t.name === tagText);

        // Add metadata suffix if metadataSuffixEnabled is true and tag exists in allTagCandidates
        let decoratedTagText = '';
        if (metadataSuffixEnabled.value && matchingTag) {
          const metadataSuffix = selectedMetadataFields.value
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|');
          if (metadataSuffix) {
            decoratedTagText = `${tagText} [${metadataSuffix}]`;
          }
        }

        // Add custom suffix to all tags if enabled
        if (customSuffixEnabled.value) {
          decoratedTagText = `${decoratedTagText}${customSuffix.value}`;
        }

        return decoratedTagText;
      });

    // Process newly added customized tags (tags added by user and not in the tag pool)
    const decoratedNewTags = Array.from(newTags.value.get(result.index) || [])
      .map(tag => {
        let decoratedNewTagText = tag.text;
        if (newTagMarkEnabled.value && !tag.isMatched) {
          if (newTagMarkPosition.value === 'prefix') {
            decoratedNewTagText = `${newTagMark.value}${tag.text}`;
          } else if (newTagMarkPosition.value === 'suffix') {
            decoratedNewTagText = `${tag.text}${newTagMark.value}`;
          }
        }

        const matchingTag = allTagCandidates.value.find(t => t.name === tag.text);

        // Add metadata suffix if enabled and tag exists in allTagCandidates
        if (metadataSuffixEnabled.value && matchingTag) {
          const metadataSuffix = selectedMetadataFields.value
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|');
          if (metadataSuffix) {
            decoratedNewTagText = `${decoratedNewTagText} [${metadataSuffix}]`;
          }
        }

        // Add custom suffix if enabled
        if (customSuffixEnabled.value) {
          decoratedNewTagText = `${decoratedNewTagText}${customSuffix.value}`;
        }

        return decoratedNewTagText;
      });

    // Combine all tags and add processed tag if enabled
    const allDecoratedTags = [...new Set([...decoratedActiveMatchedTags, ...decoratedNewTags])];
    if (processedTagEnabled.value && processedTag.value) {
      allDecoratedTags.push(processedTag.value);
    }

    // Add combined tags to row
    row.push(`${startMarker}${allDecoratedTags.join(tagDelimiter)}${endMarker}`);

    // Add other tag categories if selected
    if (includeConceptTags.value) row.push(`${startMarker}${result.tags.concept_tags.join(tagDelimiter)}${endMarker}`);
    if (includePersonOrgTags.value) row.push(`${startMarker}${result.tags.person_org_tags.join(tagDelimiter)}${endMarker}`);
    if (includeTimePlaceTags.value) row.push(`${startMarker}${result.tags.time_place_tags.join(tagDelimiter)}${endMarker}`);

    return row.join(fieldDelimiter);
  });

  // Create header row
  const headerRow = [
    ...exportFields.value,
    'Matched Tags',
    ...(includeConceptTags.value ? ['Concept Tags'] : []),
    ...(includePersonOrgTags.value ? ['Person/Org Tags'] : []),
    ...(includeTimePlaceTags.value ? ['Time/Place Tags'] : [])
  ].map(header => `${startMarker}${header}${endMarker}`);

  // Add header row to csvData
  csvData.unshift(headerRow.join(fieldDelimiter));

  const csv = csvData.join('\n');
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = 'tagged_items.csv';
  link.click();
};

// Save matched tags to Zotero
const saveTagsToZotero = async () => {
  if (!isZoteroConfigValid.value || currentSource.value !== 'Zotero') {
    ElMessage.error('Can only save to Zotero for items imported from Zotero')
    return
  }

  try {
    const { libraryId, apiKey } = zoteroConfig.value
    let successCount = 0
    let failureCount = 0

    const updatePromises = results.value.map(async (result) => {
      const item = allIndexedBiblioItems.value.find(item => item.index === result.index)
      if (!item?.key || !item?.version) return null

      try {
        // Fetch the current item to get its existing tags
        const currentItemResponse = await axios.get(
          `${zoteroBaseUrl.value}/items/${item.key}`,
          {
            headers: {
              'Zotero-API-Version': '3',
              'Authorization': `Bearer ${apiKey}`
            }
          }
        )

        // Get existing tags, excluding the tag used for import and any previously added suffixes
        const existingTags = currentItemResponse.data.data.tags
          .filter(tagObj => {
            const tag = tagObj.tag
            return tag !== zoteroConfig.value.tag &&
              (!customSuffixEnabled.value || !tag.endsWith(customSuffix.value)) &&
              (!processedTagEnabled.value || tag !== processedTag.value)
          })
          .map(tagObj => tagObj.tag)

        // Process matched tags with metadata and custom suffixes
        const decoratedActiveMatchedTags = result.tags.matched_tags
          .filter(tag => !isTagDeselected(result.index, tag))
          .map(tagName => {
            let decoratedTag = tagName
            const matchingTag = allTagCandidates.value.find(t => t.name === tagName)

            // Add metadata suffix if enabled and tag exists in allTagCandidates
            if (metadataSuffixEnabled.value && matchingTag) {
              const metadataSuffix = selectedMetadataFields.value
                .map(field => `${matchingTag[field]}`)
                .filter(Boolean)
                .join('|')
              if (metadataSuffix) {
                decoratedTag = `${decoratedTag} [${metadataSuffix}]`
              }
            }

            // Add custom suffix if enabled
            if (customSuffixEnabled.value) {
              decoratedTag = `${decoratedTag}${customSuffix.value}`
            }

            return decoratedTag
          })

        // Process new tags with metadata and custom suffixes
        const decoratedNewTags = Array.from(newTags.value.get(result.index) || [])
          .map(tag => {
            let tagText = tag.text
            // Add new tag mark if enabled
            if (newTagMarkEnabled.value && !tag.isMatched) {
              if (newTagMarkPosition.value === 'prefix') {
                tagText = `${newTagMark.value}${tagText}`
              } else if (newTagMarkPosition.value === 'suffix') {
                tagText = `${tagText}${newTagMark.value}`
              }
            }

            let decoratedTag = tagText
            const matchingTag = allTagCandidates.value.find(t => t.name === tag.text)

            // Add metadata suffix if enabled and tag exists in allTagCandidates
            if (metadataSuffixEnabled.value && matchingTag) {
              const metadataSuffix = selectedMetadataFields.value
                .map(field => `${matchingTag[field]}`)
                .filter(Boolean)
                .join('|')
              if (metadataSuffix) {
                decoratedTag = `${decoratedTag} [${metadataSuffix}]`
              }
            }

            // Add custom suffix if enabled
            if (customSuffixEnabled.value) {
              decoratedTag = `${decoratedTag}${customSuffix.value}`
            }

            return decoratedTag
          })

        // Combine all tags
        const allDecoratedTags = [...new Set([
          ...existingTags,
          ...decoratedActiveMatchedTags,
          ...decoratedNewTags
        ])]

        // Format tags for Zotero API
        const tagsToUpdate = allDecoratedTags.map(tag => ({ tag }))

        // // Add back the import tag
        // if (zoteroConfig.value.tag) {
        //   tagsToUpdate.push({ tag: zoteroConfig.value.tag })
        // }

        // Add the processed tag if enabled
        if (processedTagEnabled.value && processedTag.value) {
          tagsToUpdate.push({ tag: processedTag.value })
        }

        // Update the item with combined tags
        await axios.patch(
          `${zoteroBaseUrl.value}/items/${item.key}`,
          {
            tags: tagsToUpdate
          },
          {
            headers: {
              'Zotero-API-Version': '3',
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json',
              'If-Unmodified-Since-Version': currentItemResponse.data.version
            }
          }
        )
        successCount++
      } catch (error) {
        console.error(`Error updating item ${item.key}:`, error)
        failureCount++
        throw error
      }
    })

    await Promise.all(updatePromises)

    if (failureCount === 0) {
      ElMessage.success(`Successfully saved tags to Zotero for all ${successCount} items`)
    } else {
      ElMessage.warning(`Saved tags for ${successCount} items, failed for ${failureCount} items`)
    }
  } catch (error) {
    console.error('Error saving tags to Zotero:', error)
    let errorMessage = 'Failed to save tags to Zotero'

    if (error.response) {
      switch (error.response.status) {
        case 403:
          errorMessage = 'Invalid API key or insufficient permissions'
          break
        case 404:
          errorMessage = 'Library or item not found'
          break
        case 412:
          errorMessage = 'Item was modified since last retrieval. Please refresh and try again'
          break
        case 429:
          errorMessage = 'Too many requests. Please try again later'
          break
        default:
          errorMessage = error.response.data?.message || errorMessage
      }
    }

    ElMessage.error(errorMessage)
  }
}
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
}

.el-divider--nowrap {
  text-align: center;
  white-space: nowrap;
  padding: 0;
  width: 100%;
  max-width: 100%;
}

.el-input-delimiter {
  width: 2rem;
}

.el-pagination {
  flex-wrap: wrap;
  gap: 5px;
}

.mark-position-select {
  width: 4rem;
  height: 1.8rem;
}

.center-content {
  display: flex;
  justify-content: center;
  /* Horizontally center child elements */
  align-items: center;
  /* Vertically center child elements */
  gap: 5px;
  /* Add spacing between child elements */
  flex-wrap: wrap;
  /* Ensure responsiveness if elements exceed available space */
  text-align: center;
}

.custom-input-number {
  width: 80px;
}

.button-container {
  display: flex;
  justify-content: center;
  /* Ensures the content inside <el-col> is centered */
  align-items: center;
  /* Aligns the button vertically within the container */
}

.page-container {
  min-height: 100vh;
  /* height: 100%; */
  width: 100%;
  display: flex;
  justify-content: center;
}

.main-container {
  width: 100%;
  max-width: 1200px;
  padding: 20px;
}

.app-container {
  width: 100%;
  height: 100%;
  margin: auto auto;
}


.header-title {
  margin: 0;
  font-size: 24px;
  color: var(--el-text-color-primary);
}

.article-form {
  width: 100%;
}

.article-input {
  margin-bottom: 30px;
  padding: 20px;
  border-radius: 8px;
  background-color: var(--el-bg-color);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 20px;
}

.title-input,
.abstract-input {
  width: 100%;
}

.divider-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin: 20px 0;
  justify-content: center;
}

.csv-submit-button {
  margin-top: 20px;
}

.loading-message {
  text-align: center;
  margin: 20px 0;
  padding: 20px;
  background-color: var(--el-color-primary-light-9);
  border-radius: 4px;
  color: var(--el-text-color-primary);
}

.results {
  margin-top: 10px;
}

.wrap-text {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag-item {
  margin: 0;
}

.el-tag {
  white-space: normal; /* Allow text to wrap */
  word-break: break-word; /* Break long words if necessary */
  min-height: 1.5rem;
  height: fit-content;
  max-width: 100%; /* Ensure the tag doesn't overflow its container */
}


/* For tag matching */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.loading-icon {
  width: 14px;
  height: 14px;
  animation: rotate 1s linear infinite;
}

.tag-input-container {
  position: relative;
  display: inline-block;
}

.tag-suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: flex;
  max-height: 300px;
  background: white;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto;
}

.tag-suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-suggestion-item:hover {
  background-color: var(--el-color-primary-light-9);
}

.loading-indicator {
  padding: 8px 12px;
  color: var(--el-text-color-secondary);
  text-align: center;
}

/* Add new styles for drag and drop */
.draggable-tag {
  cursor: move;
  transition: transform 0.2s;
}

.draggable-tag:hover {
  transform: scale(1.05);
}

.draggable-tag:active {
  cursor: grabbing;
}

.droppable-area {
  min-height: 40px;
  padding: 8px;
  border: 2px dashed transparent;
  border-radius: 4px;
  transition: all 0.3s;
}

.droppable-area:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

/* New styles for CSV preview */
.csv-section {
  margin: 20px 0;
  padding: 20px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
}

.csv-preview {
  margin-top: 20px;
}

.preview-item {
  padding: 8px;
  margin: 4px 0;
  background-color: var(--el-bg-color);
  border-radius: 4px;
}

.preview-title {
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.more-items {
  margin-top: 8px;
  color: var(--el-text-color-secondary);
  font-style: italic;
}

.download-section {
  margin-top: 20px;
  text-align: center;
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.deselected-tag {
  opacity: 0.5;
  text-decoration: line-through;
}

.new-tag {
  background-color: var(--el-color-success-light-9);
  border-color: var(--el-color-success);
  color: var(--el-color-success);
}

.tag-input {
  width: 280px;
  max-width: 100%;
  margin-left: 8px;
}

.floating-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 10px;
}

.floating-button .el-button {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* Optional: Add hover effect for clickable tags */
.clickable-tag:hover {
  transform: scale(1.05);
}

.truncate-title {
  /* display: inline-block; */
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.el-form-item__label) {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}

:deep(.el-card__header) {
  text-align: center;
  padding: 10px;
  background-color: var(--el-color-primary-light-9);
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
  /* font-weight: bold; */
}

:deep(.el-descriptions__label) {
  font-weight: bold;
  width: 200px;
  padding-right: 16px;
}

:deep(.el-textarea__inner) {
  min-height: 120px !important;
}

:deep(.el-main) {
  padding: 0;
}

:deep(.el-descriptions__table) {
  width: 100%; /* Ensure the table takes full width of its container */
  max-width: 100%; /* Prevent overflow */
  table-layout: fixed; /* Ensure the table respects column widths */
}

:deep(.el-descriptions__cell) {
  word-break: break-word; /* Break long words to prevent overflow */
  white-space: normal; /* Allow text to wrap */
}


/* zotero-related */
.zotero-form {
  padding: 30px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 0px;
  margin-bottom: 0px;
}

.source-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.fetch-button {
  width: 100%;
  margin-top: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
}

:deep(.el-form-item__label) {
  font-weight: bold;
}
</style>