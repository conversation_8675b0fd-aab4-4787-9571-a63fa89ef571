<template>
  <div class="page-container">
    <el-container class="main-container">
      <el-main>
        <el-card class="app-container">
          <template #header>
            <h1 class="header-title">Bibliographic Items Tagging Tool</h1>
            <el-text>An AI-driven tool for tagging bibliographic items using tags from a tag pool. (e.g.IsisCB's tag pool,
              etc) (Ver 0.2)</el-text>
          </template>

          <!-- Model Selection Dropdown -->
          <!-- Floating buttons for advanced setting and expanding/collapsing all results -->
          <div class="floating-button">
            <div><el-button v-if="results.length" type="primary" circle @click="toggleAllResults"
                :icon="activeNames.length === paginatedResults.length ? ZoomOut : ZoomIn" /></div>
            <div><el-button type="primary" circle @click="drawerVisible = true" :icon="Setting" /></div>
          </div>

          <el-drawer title="Advanced settings" v-model="drawerVisible" :direction="drawerDirection">
            <el-divider class="el-divider--nowrap">Select Tag Generator</el-divider>
            <el-select v-model="selectedModel" placeholder="Select Tagger">
              <el-option v-for="item in modelOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
            <el-divider class="el-divider--nowrap">Customize API's URL</el-divider>
            <el-input v-model="apiUrl" placeholder="Enter API URL"></el-input>
            <el-input v-model="apiAllTagsUrl" placeholder="Enter API URL for getting the tag pool."></el-input>
          </el-drawer>


          <!-- Data Import Section -->
          <el-divider class="el-divider--nowrap">Step 1. Import Bibliographic Items</el-divider>
          <el-row justify="center">
            <el-col :span="12" :xs="24">
              <!-- Zotero Import Section -->
              <el-collapse v-model="activeDataSource">
                <el-collapse-item name="zotero">
                  <template #title>
                    <div class="source-title truncate-title">
                      <el-icon>
                        <Download />
                      </el-icon>
                      Import from Zotero (Recommended)
                    </div>
                  </template>

                  <el-form :model="zoteroConfig" label-position="top" class="zotero-form">
                    <el-form-item label="Library ID" :rules="[{ required: true, message: 'Library ID is required' }]">
                      <el-input v-model="zoteroConfig.libraryId" placeholder="Enter Zotero library or user ID">
                        <template #prefix>
                          <el-tooltip>
                            <template #content>
                              For personal library, use your <a
                                href="https://www.zotero.org/settings/security#applications" target="_blank"
                                style="color: #409EFF; text-decoration: underline;">User ID</a>. For group library, use
                              <a href="https://www.zotero.org/groups/" target="_blank"
                                style="color: #409EFF; text-decoration: underline;">Group ID</a> in the URL of the group
                              library.
                            </template>
                            <el-icon>
                              <InfoFilled />
                            </el-icon>
                          </el-tooltip>
                        </template>
                      </el-input>
                    </el-form-item>

                    <el-form-item label="API Key" :rules="[{ required: true, message: 'API key is required' }]">
                      <el-input v-model="zoteroConfig.apiKey" type="password" placeholder="Enter Zotero API key"
                        show-password>
                        <template #prefix>
                          <el-tooltip>
                            <template #content>
                              Create a new key in <a href="https://www.zotero.org/settings/security#applications"
                                target="_blank">Zotero settings</a>
                              if you do not have one.
                            </template>
                            <el-icon>
                              <Key />
                            </el-icon>
                          </el-tooltip>
                        </template>
                      </el-input>
                    </el-form-item>

                    <el-form-item label="Tag" :rules="[{ required: true, message: 'Tag is required' }]">
                      <el-select v-model="zoteroConfig.tag" placeholder="Items with this tag will be imported"
                        filterable allow-create clearable :default-first-option="true">
                        <template #prefix>
                          <el-icon>
                            <PriceTag />
                          </el-icon>
                        </template>
                        <!-- Predefined frequently used tags -->
                        <el-option v-for="(tag, index) in frequentlyUsedTagsForImport" :key="index" :label="tag"
                          :value="tag" />
                      </el-select>
                    </el-form-item>
                    <el-row justify="center">
                      <el-button type="primary" @click="fetchZoteroItems" :loading="isZoteroLoading"
                        :disabled="!isZoteroConfigValid">
                        {{ isZoteroLoading ? 'Fetching Items...' : 'Fetch Zotero Items' }}
                      </el-button>
                    </el-row>
                  </el-form>
                  <template v-if="isZoteroLoading && fetchProgress > 0">
                    <el-progress :percentage="fetchProgress" :format="format => `${format}% Complete`"
                      status="success" />
                  </template>
                </el-collapse-item>

                <el-collapse-item name="csv-upload" class="center-content">
                  <template #title>
                    <div class="source-title truncate-title">
                      <el-icon>
                        <Upload />
                      </el-icon>
                      Upload CSV
                    </div>
                  </template>

                  <el-upload class="center-content" :before-upload="handleCSVUpload" accept=".csv"
                    :show-file-list="false">
                    <el-button type="primary">
                      Upload
                    </el-button>
                  </el-upload>
                  <el-checkbox v-model="csvHasZoteroHeader">CSV exported from Zotero <el-tooltip effect="dark"
                      placement="right">
                      <template #content> Uncheck if the CSV file does not have a header. <br /> The first two columns
                        will be used as title and abstract.</template>
                      <el-icon>
                        <InfoFilled />
                      </el-icon>
                    </el-tooltip></el-checkbox>

                </el-collapse-item>
                <el-collapse-item name="manual-input">
                  <template #title>
                    <div class="source-title truncate-title">
                      <el-icon>
                        <EditPen />
                      </el-icon>
                      Manually add items
                    </div>
                  </template>
                  <el-form label-position="top" class="article-form" @submit.prevent="handleFormSubmit">
                    <div class="article-input">

                      <el-form-item label="Title" class="form-item">
                        <el-input v-model="currentInput.title" placeholder="Enter the title" @keyup.enter="appendItem"
                          class="title-input" />
                      </el-form-item>

                      <el-form-item label="Abstract" class="form-item">
                        <el-input v-model="currentInput.abstract" type="textarea" placeholder="Enter the abstract"
                          :rows="4" resize="vertical" class="abstract-input" />
                      </el-form-item>
                    </div>
                  </el-form>

                  <!-- Action buttons -->
                  <el-row justify="center" class="center-content">
                    <el-button type="primary" :icon="Plus" @click="appendItem"
                      :disabled="!currentInput.title && !currentInput.abstract">
                      Add to List
                    </el-button>
                    <el-button type="danger" :icon="Minus" @click="removeLastItem" :disabled="biblioItems.length === 0">
                      Remove Last Item
                    </el-button>
                    <el-button type="warning" :icon="RefreshLeft" @click="undoRemove"
                      :disabled="removedItems.length === 0">
                      Undo Remove ({{ removedItems.length }})
                    </el-button>
                  </el-row>
                </el-collapse-item>
              </el-collapse>
              <!-- Collapsible Form for inputting titles and abstracts -->

              <!-- Preview Section -->
              <div v-if="biblioItems.length" class="csv-preview">

                <el-card>
                  <template #header>
                    <div class="preview-header">
                      <el-text size="large">Preview of the {{ biblioItems.length }} items to be tagged:</el-text>
                      <el-tag type="info" v-if="currentSource">
                        Source: {{ currentSource }}
                      </el-tag>
                    </div>
                  </template>

                  <!-- First 3 items -->
                  <template v-if="biblioItems.length > 0">
                    <div class="preview-section">
                      <div v-for="(item, index) in biblioItems.slice(0, 3)" :key="`first-${index}`"
                        class="text item truncate-title">
                        {{ index + 1 }}. {{ item.title }}
                      </div>
                    </div>
                  </template>

                  <!-- Separator for middle items -->
                  <div v-if="biblioItems.length > 6" class="preview-separator">
                    <el-divider>
                      <el-text class="more-items">{{ biblioItems.length - 6 }} more items</el-text>
                    </el-divider>
                  </div>

                  <!-- Last 3 items -->
                  <template v-if="biblioItems.length > 3 && biblioItems.length <= 6">
                    <div class="preview-section">
                      <div v-for="(item, index) in biblioItems.slice(3)" :key="`last-${index}`"
                        class="text item truncate-title">
                        {{ index + 4 }}. {{ item.title }}
                      </div>
                    </div>
                  </template>

                  <template v-if="biblioItems.length > 6">
                    <div class="preview-section">
                      <div v-for="(item, index) in biblioItems.slice(-3)" :key="`last-${index}`"
                        class="text item truncate-title">
                        {{ biblioItems.length - 2 + index }}. {{ item.title }}
                      </div>
                    </div>
                  </template>
                </el-card>

              </div>
            </el-col>
          </el-row>
          <el-row justify="center">
            <el-col :span="16" class="center-content">

              <!-- Submit Button -->
              <el-button type="success" :icon="Check" @click="submitBiblioItems" :loading="isCSVLoading"
                :disabled="isCSVLoading || biblioItems.length === 0">
                {{ isCSVLoading ? `Processing items (${elapsedCSVTime}s)...` : 'Tag Items' }}
              </el-button>
              <el-text>in batch of</el-text><el-input-number v-model="batchSize" :min="1"
                :max="Math.max(biblioItems.length, 1)" :disabled="isCSVLoading || biblioItems.length === 0"
                :placeholder="`${suggestedBatchSize}`" controls-position="right" class="custom-input-number" />
              <!-- Clear All Button -->
              <el-button type="danger" :icon="Delete" @click="clearAllItems"
                :disabled="isCSVLoading || biblioItems.length === 0">
                Clear All Items
              </el-button>
            </el-col>
          </el-row>
          <el-row justify="center">
            <el-col :span="12">

            </el-col>
          </el-row>


          <!-- Loading message -->
          <div v-if="isLoading || isCSVLoading" class="loading-message">
            <p>Generating tags... This may take a few minutes.</p>
            <p>Time elapsed: {{ isCSVLoading ? elapsedCSVTime : elapsedTime }} seconds</p>
          </div>

          <!-- Results section -->
          <el-divider class="el-divider--nowrap">Step 2. Review and Edit Matched Tags</el-divider>
          <div v-if="results.length" class="results">
            <el-row justify="center" class="button-container">
              <el-col :span="24">
                <el-button plain round @click="toggleAllResults">
                  <el-icon>
                    <component :is="activeNames.length === paginatedResults.length ? ZoomOut : ZoomIn" />
                  </el-icon>
                  {{ activeNames.length === paginatedResults.length ? 'Collapse All' : 'Expand All' }}
                </el-button>
              </el-col>
            </el-row>
            <el-row justify="center">
              <el-col :span="24">
                <el-collapse v-model="activeNames" accordion>
                  <el-collapse-item v-for="(result, index) in paginatedResults" :key="result.index"
                    :name="result.index">
                    <template #title>
                      <span class="truncate-title">Item {{ result.index + 1 }}: {{ result.title }}</span>
                    </template>
                    <el-row></el-row>
                    <el-descriptions :column="1" border direction="vertical">
                      <el-descriptions-item label="Title">
                        <el-text>{{ result.title }}</el-text>
                      </el-descriptions-item>
                      <el-descriptions-item label="Abstract">
                        <el-text>{{ getAbstractByIndex(result.index) }}</el-text>
                      </el-descriptions-item>

                      <el-descriptions-item>
                        <template #label>
                          Matched Tags
                          <el-tooltip effect="dark" :placement="screenIsPortrait ? 'top' : 'right'">
                            <template #content> This is the final list of tags for the item.<br />
                              You can click a tag to deselect it, add new tags using the input box, <br />or drag and
                              drop/double-click tags from other sections below to include them.</template>
                            <el-icon>
                              <InfoFilled />
                            </el-icon>
                          </el-tooltip>
                        </template>

                        <div class="tags-container droppable-area" @dragover="handleDragOver"
                          @drop="handleDrop($event, index)">
                          <!-- Existing matched tags -->
                          <el-tag v-for="tag in result.tags.matched_tags" :key="tag"
                            :class="{ 'deselected-tag': isTagDeselected(result.index, tag) }"
                            class="tag-item clickable-tag" @click="toggleTag(result.index, tag)">
                            {{ tag }}
                          </el-tag>

                          <!-- New tags with different styles -->
                          <template v-for="tag in Array.from(newTags.get(result.index) || [])" :key="tag.text">
                            <el-tag :type="tag.isMatched ? 'primary' : 'warning'" closable
                              @close="removeNewTag(result.index, tag.text)">
                              {{ tag.text }}
                            </el-tag>
                          </template>

                          <!-- Enhanced tag input with suggestions -->
                          <div class="tag-input-container">
                            <el-input class="tag-input" placeholder="Type to search or Enter to add customized tags..."
                              v-model="inputNewTagValue" @focus="() => handleInputFocus(result.index)"
                              @input="(value) => handleSearchInput(value, result.index)"
                              @keyup.enter="(event) => addNewTag(result.index, event)"
                              @blur="() => closeSuggestions(result.index)" size="small"
                              :loading="isFetchingTags && !hasLoadedTags.value">
                              <template #prefix>
                                <el-icon v-if="isFetchingTags && !hasLoadedTags.value" class="is-loading">
                                  <Loading />
                                </el-icon>
                              </template>
                            </el-input>

                            <!-- Tag suggestions dropdown -->
                            <div
                              v-show="showTagSuggestions.get(result.index) && getFilteredTags(searchQuery.get(result.index)).length > 0"
                              class="tag-suggestions-dropdown">
                              <template v-if="!isFetchingTags || hasLoadedTags.value">
                                <div v-for="tag in getFilteredTags(searchQuery.get(result.index))" :key="tag"
                                  class="tag-suggestion-item" @mousedown="() => selectTag(result.index, tag)">
                                  {{ tag }}
                                </div>
                              </template>
                              <div v-else class="loading-indicator">
                                Loading tags...
                              </div>
                            </div>
                          </div>
                        </div>
                      </el-descriptions-item>

                      <el-descriptions-item label="Concept Tags">
                        <div class="tags-container">
                          <el-tag v-for="tag in result.tags.concept_tags" :key="tag" type="info"
                            class="tag-item draggable-tag" draggable="true"
                            @dragstart="handleDragStart($event, tag, 'concept')"
                            @dblclick="(event) => addNewTagDoubleClick(result.index, tag)">
                            {{ tag }}
                          </el-tag>
                        </div>
                      </el-descriptions-item>

                      <el-descriptions-item label="Person/Organization Tags">
                        <div class="tags-container">
                          <el-tag v-for="tag in result.tags.person_org_tags" :key="tag" type="info"
                            class="tag-item draggable-tag" draggable="true"
                            @dragstart="handleDragStart($event, tag, 'person_org')"
                            @dblclick="(event) => addNewTagDoubleClick(result.index, tag)">
                            {{ tag }}
                          </el-tag>
                        </div>
                      </el-descriptions-item>

                      <el-descriptions-item label="Time/Place Tags">
                        <div class="tags-container">
                          <el-tag v-for="tag in result.tags.time_place_tags" :key="tag" type="info"
                            class="tag-item draggable-tag" draggable="true"
                            @dragstart="handleDragStart($event, tag, 'time_place')"
                            @dblclick="(event) => addNewTagDoubleClick(result.index, tag)">
                            {{ tag }}
                          </el-tag>
                        </div>
                      </el-descriptions-item>
                    </el-descriptions>
                  </el-collapse-item>
                </el-collapse>
              </el-col>
            </el-row>
            <!-- Pagination component -->
            <el-row justify="center" class="mt-4">
              <el-col :span="24">
                <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
                  :page-sizes="[5, 10, 20, 50]" :total="results.length" layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange" @current-change="handleCurrentChange" />
              </el-col>
            </el-row>
          </div>

          <!-- Step 3. Export Result -->
          <el-divider class="el-divider--nowrap">Step 3. Export Result</el-divider>
          <div v-if="results.length" class="download-section">
            <el-row :gutter="20" justify="center" class="center-content">
              <!-- Prefix or suffix for new customized tags -->
              <el-col :span="6" :xs="24">
                <el-checkbox v-model="newTagMarkEnabled">Add <el-select v-model="newTagMarkPosition"
                    :disabled="!newTagMarkEnabled" size="small" class="mark-position-select">
                    <el-option label="prefix" value="prefix"></el-option>
                    <el-option label="suffix" value="suffix"></el-option>
                  </el-select>
                  to customized tags</el-checkbox>
                <el-input :disabled="!newTagMarkEnabled" v-model="newTagMark" placeholder="Enter prefix">
                  <template #prepend>
                    <el-tooltip
                      content="This prefix will be added to all customized tags (new tags not in the tag pool) in the exported CSV"
                      placement="top">
                      <el-icon>
                        <InfoFilled />
                      </el-icon>
                    </el-tooltip>
                  </template>
                </el-input>
                <!-- Metadata suffix section -->
                <el-checkbox v-model="metadataSuffixEnabled">
                  Add metadata suffix to matched tags
                </el-checkbox>
                <el-select :disabled="!metadataSuffixEnabled || !(availableMetadataFields.length > 0)"
                  v-model="selectedMetadataFields" multiple placeholder="Select metadata fields">
                  <el-option v-for="field in availableMetadataFields" :key="field.value" :label="field.label"
                    :value="field.value" />
                </el-select>
              </el-col>

              <el-col :span="6" :xs="24">
                <!-- Custom suffix section -->
                <el-checkbox v-model="customSuffixEnabled">
                  Add custom suffix
                </el-checkbox>
                <el-input v-model="customSuffix" placeholder="Enter custom suffix" :disabled="!customSuffixEnabled"
                  class="suffix-input">
                  <template #prepend>
                    <el-tooltip content="This suffix will be added to all matched tags when saving to Zotero"
                      placement="top">
                      <el-icon>
                        <InfoFilled />
                      </el-icon>
                    </el-tooltip>
                  </template>
                </el-input>
                <!-- "Processed by this tool" tag -->
                <el-checkbox v-model="processedTagEnabled">
                  Add an extra tag for all processed items
                </el-checkbox>
                <el-input v-model="processedTag" placeholder="Enter marker tag" :disabled="!processedTagEnabled">
                  <template #prepend>
                    <el-tooltip content="This tag will be added to all items processed by this tool" placement="top">
                      <el-icon>
                        <InfoFilled />
                      </el-icon>
                    </el-tooltip>
                  </template>
                </el-input>
              </el-col>

              <el-col :span="6" :xs="24" class="center-content">
                <!-- Preview section -->
                <div><el-text size="default">Preview of tags to be added: </el-text></div>
                <el-tag v-if="!(metadataSuffixEnabled && selectedMetadataFields.length > 0) && !customSuffixEnabled"
                  type="primary">
                  matched-tags
                </el-tag>
                <el-tag v-if="!newTagMarkEnabled && !customSuffixEnabled" type="warning">
                  customized-tags
                </el-tag>

                <el-tag v-if="metadataSuffixEnabled && selectedMetadataFields.length > 0 && customSuffixEnabled"
                  class="preview-tag" type="primary">
                  matched-tags{{ getPreviewSuffix() }}{{ customSuffix }}
                </el-tag>
                <el-tag v-if="metadataSuffixEnabled && selectedMetadataFields.length > 0 && !customSuffixEnabled"
                  class="preview-tag" type="primary">
                  matched-tags{{ getPreviewSuffix() }}
                </el-tag>
                <el-tag v-if="!metadataSuffixEnabled && customSuffixEnabled" type="primary">
                  matched-tags{{ customSuffix }}
                </el-tag>

                <el-tag v-if="!newTagMarkEnabled && customSuffixEnabled" type="warning">
                  customized-tags{{ customSuffix }}
                </el-tag>
                <el-tag v-if="newTagMarkEnabled && newTagMarkPosition === 'prefix' && customSuffixEnabled"
                  type="warning">
                  {{ newTagMark }}customized-tags{{ customSuffix }}
                </el-tag>
                <el-tag v-if="newTagMarkEnabled && newTagMarkPosition === 'suffix' && customSuffixEnabled"
                  type="warning">
                  customized-tags{{ newTagMark }}{{ customSuffix }}
                </el-tag>
                <el-tag v-if="newTagMarkEnabled && newTagMarkPosition === 'prefix' && !customSuffixEnabled"
                  type="warning">
                  {{ newTagMark }}customized-tags
                </el-tag>
                <el-tag v-if="newTagMarkEnabled && newTagMarkPosition === 'suffix' && !customSuffixEnabled"
                  type="warning">
                  customized-tags{{ newTagMark }}
                </el-tag>

                <el-tag v-if="processedTagEnabled" type="info">
                  {{ processedTag }}
                </el-tag>
              </el-col>
            </el-row>

            <el-row :gutter="10" justify="center" class="mt-4 center-content">
              <el-col :span="6" :xs="24" class="button-container">
                <el-button type="success" @click="downloadCSV">
                  Download To CSV
                </el-button>
                <el-button type="info" @click="openExportDialog" :disabled="results.length === 0">
                  CSV Options
                </el-button>

                <!-- Add this dialog for export options -->
                <el-dialog v-model="showExportDialog" title="CSV Export Options" :width="screenIsPortrait ? '90%' : '50%'">
                  <el-form :model="exportOptions">
                    <el-divider class="el-divider--nowrap">Fields to export</el-divider>
                    <el-checkbox v-model="selectAll" @change="handleSelectAll">Select All</el-checkbox>
                    <el-row :gutter="5" justify="left">
                      <el-checkbox-group v-model="exportFields">
                        <el-checkbox v-for="field in availableFields" :key="field" :label="field" :value="field">
                          {{ field }}
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-row>
                    <el-divider class="el-divider--nowrap">Include auto tags</el-divider>
                    <el-row :gutter="10" justify="center">
                      <el-checkbox v-model="includeConceptTags">Concept tags</el-checkbox>
                      <el-checkbox v-model="includePersonOrgTags">Person/Organization tags</el-checkbox>
                      <el-checkbox v-model="includeTimePlaceTags">Time/Place tags</el-checkbox>
                    </el-row>

                    <el-divider class="el-divider--nowrap">Delimiter settings</el-divider>
                    <el-row :gutter="10" justify="center" class="center-content">
                      <el-col :span="6" :xs="24">
                        <el-text>Field delimiter: </el-text>
                        <el-input v-model="exportOptions.fieldDelimiter" class="el-input-delimiter"></el-input>
                      </el-col>
                      <el-col :span="6" :xs="24">
                        <el-text>Tag delimiter: </el-text>
                        <el-input v-model="exportOptions.tagDelimiter" class="el-input-delimiter"></el-input>
                      </el-col>
                      <el-col :span="6" :xs="24">
                        <el-text>Field enclosure: </el-text>
                        <el-input v-model="exportOptions.valueMarkers" class="el-input-delimiter"></el-input>
                      </el-col>
                    </el-row>
                  </el-form>
                  <template #footer>
                    <span class="dialog-footer">
                      <el-button @click="showExportDialog = false">Cancel</el-button>
                      <el-button type="success" @click="handleExport">Download To CSV</el-button>
                    </span>
                  </template>
                </el-dialog>
              </el-col>
              <el-col :span="6" :xs="24" class="button-container">
                <el-tooltip
                  :disabled="!(!isZoteroConfigValid || currentSource !== 'Zotero')"
                  content="Please import items from Zotero to enable this option."
                  placement="top"
                >
                  <el-button
                    type="success"
                    @click="saveTagsToZotero"
                    :disabled="!isZoteroConfigValid || currentSource !== 'Zotero'"
                  >
                    Save Matched Tags to Zotero
                  </el-button>
                </el-tooltip>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, toRaw, isReactive } from 'vue'
import axios from 'axios'
import Papa from 'papaparse'
import { debounce } from 'lodash'
import {
  ElContainer,
  ElMain,
  ElCard,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElDivider,
  ElCollapse,
  ElCollapseItem,
  ElDescriptions,
  ElDescriptionsItem,
  ElTag,
  ElMessage,
  ElMessageBox,
  ElUpload,
  ElCheckbox,
  ElCheckboxGroup,
  ElDialog
} from 'element-plus'
import { Plus, Minus, EditPen, Check, Upload, ZoomIn, ZoomOut, RefreshLeft, Download, InfoFilled, Key, PriceTag, Delete, Loading, Setting } from '@element-plus/icons-vue'

const drawerVisible = ref(false)
const screenIsPortrait = ref(window.innerHeight > window.innerWidth);
const drawerDirection = computed(() => {
  return screenIsPortrait.value ? 'btt' : 'rtl'; // 'btt' = bottom, 'rtl' = right
});

const currentInput = ref({ title: '', abstract: '' })
const removedItems = ref([])
const biblioItems = ref([]) // This is for storing the original items imported from data sources
const allIndexedBiblioItems = ref([]) // This is the indexed version of biblioItems. It will be populated when calling submitBiblioItems function.
const results = ref([]) // This is for storing the results from the API.

// Pagination
const currentPage = ref(1)
const pageSize = ref(10)
const paginatedResults = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value
  const endIndex = startIndex + pageSize.value
  return results.value.slice(startIndex, endIndex)
})

const deselectedTags = ref(new Set())
const newTags = ref(new Map())
const inputNewTagValue = ref('')
const activeNames = ref([])
const isLoading = ref(false)

const apiUrl = ref('https://tagger.yizehu.com/api/generate_tags')
const apiAllTagsUrl = ref('https://tagger.yizehu.com/api/tags/all')
const batchSize = ref(null)
const suggestedBatchSize = computed(() => {
  const length = biblioItems.value.length;
  if (length === 0) {
    return 1; // Default when there are no items
  } else if (length <= 5) {
    return length; // Process all items if there are 5 or fewer
  } else if (length <= 50) {
    return Math.ceil(length / 5); // Split into ~5 batches for datasets between 6 and 50
  } else if (length <= 200) {
    return Math.ceil(length / 10); // Split into ~10 batches for datasets between 51 and 200
  } else {
    return Math.ceil(length / 20); // Split into ~20 batches for datasets larger than 200
  }
});

const csvHasZoteroHeader = ref(true);
const isCSVLoading = ref(false)
const elapsedTime = ref(0)
const elapsedCSVTime = ref(0)
let timerInterval = null
let csvTimerInterval = null

// Add model selection related refs
const selectedModel = ref('gpt-4o-mini')
const modelOptions = ref([
  { value: 'gpt-4o-mini', label: 'gpt-4o-mini (3~4s/item)' },
  { value: 'pytextrank', label: 'pytextrank (very fast)' }
  // Add more model options here
])

// TAG CONFIGURATION
const allTagCandidates = ref([]) // Store all tags from the curated tag pool
const tagNames = computed(() => allTagCandidates.value.map(tag => tag.name))
const searchQuery = ref(new Map()) // Map to store search query for each result index
const showTagSuggestions = ref(new Map()) // Map to store dropdown state for each result index
const isFetchingTags = ref(false)
const hasLoadedTags = ref(false) // Track if tags have been loaded
// Configuration for decorating tags after reviewing
const newTagMarkEnabled = ref(true)
const newTagMarkPosition = ref('suffix')
const newTagMark = ref('[NEW]')
const metadataSuffixEnabled = ref(true)
const selectedMetadataFields = ref(['record_id'])
const customSuffixEnabled = ref(false)
const customSuffix = ref('[IsisCBtag]')
const processedTagEnabled = ref(true)
const processedTag = ref('processed-by-tagger')
// Available metadata fields (excluding 'name')
const availableMetadataFields = ref([]);
// Function to update availableMetadataFields
const updateMetadataFields = () => {
  if (!allTagCandidates.value || allTagCandidates.value.length === 0) {
    availableMetadataFields.value = [{ label: 'N/A', value: 'N/A' }]; // Default value
    return;
  }

  const firstObject = allTagCandidates.value[0];
  const fields = Object.keys(toRaw(firstObject || {})); // Guard against null/undefined
  const filteredFields = fields.filter(field => field !== 'name');

  availableMetadataFields.value = filteredFields.map(field => ({
    label: field,
    value: field
  }));
};

watch(allTagCandidates, updateMetadataFields, { immediate: true });
// Function to generate preview suffix
const getPreviewSuffix = () => {
  let suffix = ''

  if (metadataSuffixEnabled.value && selectedMetadataFields.value.length > 0) {
    suffix += `[${selectedMetadataFields.value.join('|')}]` // Example preview
  }

  return suffix
}

// Function to update screen orientation
const updateScreenOrientation = () => {
  screenIsPortrait.value = window.innerHeight > window.innerWidth;
};

// Add event listener to handle screen resizing
onMounted(() => {
  window.addEventListener('resize', updateScreenOrientation);
});

// Clean up the event listener when the component is unmounted
onUnmounted(() => {
  window.removeEventListener('resize', updateScreenOrientation);
});

// Zotero-related
const zoteroConfig = ref({
  libraryId: '',
  apiKey: '',
  tag: ''
})
const zoteroBaseUrl = ref('')
const MAX_ITEMS_PER_REQUEST = 100
const isZoteroLoading = ref(false)
const frequentlyUsedTagsForImport = ref([
  'to-be-tagged',
  'IsisCB project',
]);
const activeDataSource = ref(['csv']) // For collapse control
const currentSource = ref('') // To track data source

// Add computed property for form validation
const isZoteroConfigValid = computed(() => {
  const { libraryId, apiKey, tag } = zoteroConfig.value
  return libraryId && apiKey && tag
})

// Add new function to fetch Zotero items
const fetchZoteroItems = async () => {
  if (!isZoteroConfigValid.value) return

  try {
    isZoteroLoading.value = true
    const { libraryId, apiKey, tag } = zoteroConfig.value

    // Initialize base URL for group library
    zoteroBaseUrl.value = `https://api.zotero.org/groups/${libraryId}`;
    let isGroupLibrary = true;

    // Test if the `libraryId` belongs to a group or personal library
    let testResponse;
    try {
      // Test request for group library
      testResponse = await axios.get(`${zoteroBaseUrl.value}/items`, {
        params: {
          tag,
          limit: 1,
          includeTrashed: 0
        },
        headers: {
          'Zotero-API-Version': '3',
          'Authorization': `Bearer ${apiKey}`
        }
      });

      // If the response returns an empty list, switch to personal library
      if (Array.isArray(testResponse.data) && testResponse.data.length === 0) {
        // Switch to personal library URL
        zoteroBaseUrl.value = `https://api.zotero.org/users/${libraryId}`;
        isGroupLibrary = false;

        // Test request for personal library
        testResponse = await axios.get(`${zoteroBaseUrl.value}/items`, {
          params: {
            tag,
            limit: 1,
            includeTrashed: 0
          },
          headers: {
            'Zotero-API-Version': '3',
            'Authorization': `Bearer ${apiKey}`
          }
        });

        // If the personal library is also empty, handle appropriately
        if (Array.isArray(testResponse.data) && testResponse.data.length === 0) {
          throw new Error('Both group and personal libraries returned empty lists.');
        }
      }
    } catch (error) {
      // Handle unexpected errors during the test request
      if ((Array.isArray(testResponse.data) && testResponse.data.length === 0) && error.response && error.response.status === 403) {
        ElMessage.error('No item with the given tag found. Please make sure all items are synced to the online library, and check your library ID and API key permissions. (Empty or 403 Forbidden)');
        return;
      }
      ElMessage.error('Failed to connect to Zotero library. Please check your configuration.');
      return;
    }

    // First, get total items count
    const countResponse = await axios.get(
      `${zoteroBaseUrl.value}/items`,
      {
        params: {
          tag,
          limit: 1,
          includeTrashed: 0
        },
        headers: {
          'Zotero-API-Version': '3',
          'Authorization': `Bearer ${apiKey}`
        }
      }
    )

    const totalItems = parseInt(countResponse.headers['total-results'])
    const numberOfRequests = Math.ceil(totalItems / MAX_ITEMS_PER_REQUEST)
    let allFetchedItems = []

    // Fetch all items in parallel
    const requests = Array.from({ length: numberOfRequests }, (_, index) => {
      return axios.get(
        `${zoteroBaseUrl.value}/items`,
        {
          params: {
            tag,
            limit: MAX_ITEMS_PER_REQUEST,
            start: index * MAX_ITEMS_PER_REQUEST,
            includeTrashed: 0,
            itemType: '-attachment || note' // Exclude attachments and notes
          },
          headers: {
            'Zotero-API-Version': '3',
            'Authorization': `Bearer ${apiKey}`
          }
        }
      )
    })

    const responses = await Promise.all(requests)

    // Process all responses
    responses.forEach(response => {
      const items = response.data.map(item => ({
        key: item.key,
        version: item.version,
        title: item.data.title || '',
        abstract: item.data.abstractNote || '',
        creators: item.data.creators || [],
        itemType: item.data.itemType || '',
        date: item.data.date || '',
        publicationTitle: item.data.publicationTitle || '',
        volume: item.data.volume || '',
        issue: item.data.issue || '',
        pages: item.data.pages || '',
        url: item.data.url || '',
        extra: item.data.extra || '',
      })).filter(item => item.title || item.abstract)

      allFetchedItems = [...allFetchedItems, ...items]
    })

    // Update the items list
    biblioItems.value = allFetchedItems
    removedItems.value = [] // Clear removed items history
    currentSource.value = 'Zotero'

    ElMessage.success(`Successfully fetched ${allFetchedItems.length} items from Zotero`)

    // // Clear sensitive data
    // zoteroConfig.value = {
    //   libraryId: '',
    //   apiKey: '',
    //   tag: ''
    // }

  } catch (error) {
    console.error('Error fetching Zotero items:', error)
    let errorMessage = 'Failed to fetch items from Zotero'

    if (error.response) {
      switch (error.response.status) {
        case 403:
          errorMessage = 'Invalid API key or insufficient permissions'
          break
        case 404:
          errorMessage = 'Library not found. Please check your Library ID'
          break
        case 429:
          errorMessage = 'Too many requests. Please try again later'
          break
        default:
          errorMessage = error.response.data?.message || errorMessage
      }
    }

    ElMessage.error(errorMessage)
  } finally {
    isZoteroLoading.value = false
  }
}

// Add progress tracking (optional)
const fetchProgress = ref(0)
const updateProgress = (current, total) => {
  fetchProgress.value = Math.round((current / total) * 100)
}

const startTimer = (isCSV = false) => {
  if (isCSV) {
    elapsedCSVTime.value = 0
    csvTimerInterval = setInterval(() => {
      elapsedCSVTime.value++
    }, 1000)
  } else {
    elapsedTime.value = 0
    timerInterval = setInterval(() => {
      elapsedTime.value++
    }, 1000)
  }
}

const stopTimer = (isCSV = false) => {
  if (isCSV) {
    if (csvTimerInterval) {
      clearInterval(csvTimerInterval)
      csvTimerInterval = null
    }
  } else {
    if (timerInterval) {
      clearInterval(timerInterval)
      timerInterval = null
    }
  }
}

const submitBiblioItems = async () => {
  try {
    isCSVLoading.value = true
    startTimer(true)

    // Clear previous results and collapse states
    results.value = []
    activeNames.value = []

    // VERY IMPORTANT: Index all items. After this step, allIndexedBiblioItems should be used instead of biblioItems.
    allIndexedBiblioItems.value = biblioItems.value.map((item, idx) => ({
      ...item,
      index: idx,
    }))

    // Use the user's chosen batch size (or suggested if not set)
    const size = batchSize.value || suggestedBatchSize.value

    // Process items in batches
    for (let i = 0; i < allIndexedBiblioItems.value.length; i += size) {
      const batch = allIndexedBiblioItems.value.slice(i, i + size)
      const response = await axios.post(`${apiUrl.value}`, {
        model: selectedModel.value,
        items: batch.map(article => ({
          key: article.key,
          title: article.title,
          abstract: article.abstract,
          index: article.index
        }))
      })

      results.value.push(...response.data)
      ElMessage.success(`Batch ${Math.floor(i / size) + 1} tagged successfully!`)
      await fetchAllTags()
    }
  } catch (error) {
    console.error('Error batch-tagging articles:', error)
    ElMessage.error('An error occurred while batch-tagging articles.')
  } finally {
    stopTimer(true)
    isCSVLoading.value = false
  }
}

const clearAllItems = () => {
  ElMessageBox.confirm('This will remove all items. Continue?', 'Warning', {
    confirmButtonText: 'Yes',
    cancelButtonText: 'No',
    type: 'warning',
  }).then(() => {
    biblioItems.value = []
    allIndexedBiblioItems.value = []
    results.value = []
    removedItems.value = []
    currentSource.value = ''
    batchSize.value = null
    suggestedBatchSize.value = null
    deselectedTags.value = new Set()
    newTags.value = new Map()
    // allTagCandidates.value = []
    ElMessage({
      type: 'success',
      message: 'All items have been cleared',
      duration: 2000
    })
  }).catch((error) => {
    console.error('Error in clearAllItems:', error)
  })
}

const appendItem = () => {
  if (currentInput.value.title.trim() || currentInput.value.abstract.trim()) {
    biblioItems.value.push({
      title: currentInput.value.title.trim(),
      abstract: currentInput.value.abstract.trim()
    })
    // Clear input fields after adding
    currentInput.value = { title: '', abstract: '' }
    ElMessage.success('Item added successfully!')
  }
}

const removeLastItem = () => {
  if (biblioItems.value.length > 0) {
    const removedItem = biblioItems.value.pop()
    removedItems.value.push(removedItem)
    ElMessage.success('Last item removed. Click Undo to restore.')
  }
}

const undoRemove = () => {
  if (removedItems.value.length > 0) {
    const itemToRestore = removedItems.value.pop()
    biblioItems.value.push(itemToRestore)
    ElMessage.success('Item restored successfully!')
  }
}

const handleFormSubmit = (event) => {
  event.preventDefault() // Prevent default form submission
  appendItem() // Call appendItem function
}


// Handle CSV file upload
const handleCSVUpload = (file, hasHeader = csvHasZoteroHeader.value) => {
  Papa.parse(file, {
    complete: (result) => {
      let data;
      if (hasHeader) {
        // If the CSV has a header, use named fields
        data = result.data
          .filter(row => row.Key) // Ensure the row has a Key (or any required field)
          .map((row) => ({
            key: row.Key?.replace(/^"|"$/g, '').trim() || '',
            itemType: row['Item Type']?.replace(/^"|"$/g, '').trim() || '',
            publicationYear: row['Publication Year']?.replace(/^"|"$/g, '').trim() || '',
            author: row.Author?.replace(/^"|"$/g, '').trim() || '',
            title: row.Title?.replace(/^"|"$/g, '').trim() || '',
            publicationTitle: row['Publication Title']?.replace(/^"|"$/g, '').trim() || '',
            isbn: row.ISBN?.replace(/^"|"$/g, '').trim() || '',
            issn: row.ISSN?.replace(/^"|"$/g, '').trim() || '',
            doi: row.DOI?.replace(/^"|"$/g, '').trim() || '',
            url: row.Url?.replace(/^"|"$/g, '').trim() || '',
            abstract: row['Abstract Note']?.replace(/^"|"$/g, '').trim() || '',
            date: row.Date?.replace(/^"|"$/g, '').trim() || '',
            dateAdded: row['Date Added']?.replace(/^"|"$/g, '').trim() || '',
            dateModified: row['Date Modified']?.replace(/^"|"$/g, '').trim() || '',
            accessDate: row['Access Date']?.replace(/^"|"$/g, '').trim() || '',
            pages: row.Pages?.replace(/^"|"$/g, '').trim() || '',
            numPages: row['Num Pages']?.replace(/^"|"$/g, '').trim() || '',
            issue: row.Issue?.replace(/^"|"$/g, '').trim() || '',
            volume: row.Volume?.replace(/^"|"$/g, '').trim() || '',
            numberOfVolumes: row['Number Of Volumes']?.replace(/^"|"$/g, '').trim() || '',
            journalAbbreviation: row['Journal Abbreviation']?.replace(/^"|"$/g, '').trim() || '',
            shortTitle: row['Short Title']?.replace(/^"|"$/g, '').trim() || '',
            series: row.Series?.replace(/^"|"$/g, '').trim() || '',
            seriesNumber: row['Series Number']?.replace(/^"|"$/g, '').trim() || '',
            seriesText: row['Series Text']?.replace(/^"|"$/g, '').trim() || '',
            seriesTitle: row['Series Title']?.replace(/^"|"$/g, '').trim() || '',
            publisher: row.Publisher?.replace(/^"|"$/g, '').trim() || '',
            place: row.Place?.replace(/^"|"$/g, '').trim() || '',
            language: row.Language?.replace(/^"|"$/g, '').trim() || '',
            rights: row.Rights?.replace(/^"|"$/g, '').trim() || '',
            type: row.Type?.replace(/^"|"$/g, '').trim() || '',
            archive: row.Archive?.replace(/^"|"$/g, '').trim() || '',
            archiveLocation: row['Archive Location']?.replace(/^"|"$/g, '').trim() || '',
            libraryCatalog: row['Library Catalog']?.replace(/^"|"$/g, '').trim() || '',
            callNumber: row['Call Number']?.replace(/^"|"$/g, '').trim() || '',
            extra: row.Extra?.replace(/^"|"$/g, '').trim() || '',
            notes: row.Notes?.replace(/^"|"$/g, '').trim() || '',
            fileAttachments: row['File Attachments']?.replace(/^"|"$/g, '').trim() || '',
            linkAttachments: row['Link Attachments']?.replace(/^"|"$/g, '').trim() || '',
            editor: row.Editor?.replace(/^"|"$/g, '').trim() || '',
            seriesEditor: row['Series Editor']?.replace(/^"|"$/g, '').trim() || '',
            translator: row.Translator?.replace(/^"|"$/g, '').trim() || '',
            contributor: row.Contributor?.replace(/^"|"$/g, '').trim() || '',
            attorneyAgent: row['Attorney Agent']?.replace(/^"|"$/g, '').trim() || '',
            bookAuthor: row['Book Author']?.replace(/^"|"$/g, '').trim() || '',
            castMember: row['Cast Member']?.replace(/^"|"$/g, '').trim() || '',
            commenter: row.Commenter?.replace(/^"|"$/g, '').trim() || '',
            composer: row.Composer?.replace(/^"|"$/g, '').trim() || '',
            cosponsor: row.Cosponsor?.replace(/^"|"$/g, '').trim() || '',
            counsel: row.Counsel?.replace(/^"|"$/g, '').trim() || '',
            interviewer: row.Interviewer?.replace(/^"|"$/g, '').trim() || '',
            producer: row.Producer?.replace(/^"|"$/g, '').trim() || '',
            recipient: row.Recipient?.replace(/^"|"$/g, '').trim() || '',
            reviewedAuthor: row['Reviewed Author']?.replace(/^"|"$/g, '').trim() || '',
            scriptwriter: row.Scriptwriter?.replace(/^"|"$/g, '').trim() || '',
            wordsBy: row['Words By']?.replace(/^"|"$/g, '').trim() || '',
            guest: row.Guest?.replace(/^"|"$/g, '').trim() || '',
            number: row.Number?.replace(/^"|"$/g, '').trim() || '',
            edition: row.Edition?.replace(/^"|"$/g, '').trim() || '',
            runningTime: row['Running Time']?.replace(/^"|"$/g, '').trim() || '',
            scale: row.Scale?.replace(/^"|"$/g, '').trim() || '',
            medium: row.Medium?.replace(/^"|"$/g, '').trim() || '',
            artworkSize: row['Artwork Size']?.replace(/^"|"$/g, '').trim() || '',
            filingDate: row['Filing Date']?.replace(/^"|"$/g, '').trim() || '',
            applicationNumber: row['Application Number']?.replace(/^"|"$/g, '').trim() || '',
            assignee: row.Assignee?.replace(/^"|"$/g, '').trim() || '',
            issuingAuthority: row['Issuing Authority']?.replace(/^"|"$/g, '').trim() || '',
            country: row.Country?.replace(/^"|"$/g, '').trim() || '',
            meetingName: row['Meeting Name']?.replace(/^"|"$/g, '').trim() || '',
            conferenceName: row['Conference Name']?.replace(/^"|"$/g, '').trim() || '',
            court: row.Court?.replace(/^"|"$/g, '').trim() || '',
            references: row.References?.replace(/^"|"$/g, '').trim() || '',
            reporter: row.Reporter?.replace(/^"|"$/g, '').trim() || '',
            legalStatus: row['Legal Status']?.replace(/^"|"$/g, '').trim() || '',
            priorityNumbers: row['Priority Numbers']?.replace(/^"|"$/g, '').trim() || '',
            programmingLanguage: row['Programming Language']?.replace(/^"|"$/g, '').trim() || '',
            version: row.Version?.replace(/^"|"$/g, '').trim() || '',
            system: row.System?.replace(/^"|"$/g, '').trim() || '',
            code: row.Code?.replace(/^"|"$/g, '').trim() || '',
            codeNumber: row['Code Number']?.replace(/^"|"$/g, '').trim() || '',
            section: row.Section?.replace(/^"|"$/g, '').trim() || '',
            session: row.Session?.replace(/^"|"$/g, '').trim() || '',
            committee: row.Committee?.replace(/^"|"$/g, '').trim() || '',
            history: row.History?.replace(/^"|"$/g, '').trim() || '',
            legislativeBody: row['Legislative Body']?.replace(/^"|"$/g, '').trim() || ''
          }))
          .filter(item => item.title !== '' || item.abstractNote !== ''); // Filter out empty items
      } else {
        // If the CSV does not have a header, use positional indexing
        data = result.data
          .filter(row => row.length >= 2) // Ensure the row has at least two columns
          .map((row) => ({
            title: row[0]?.replace(/^"|"$/g, '').trim() || '',
            abstract: row[1]?.replace(/^"|"$/g, '').trim() || ''
          }))
          .filter(item => item.title !== '' || item.abstract !== ''); // Filter out empty items
      }

      batchSize.value = null;
      biblioItems.value = data;
      removedItems.value = []; // Clear removed items history when new CSV is uploaded
      currentSource.value = 'local file';
      ElMessage.success(`CSV file processed: ${data.length} articles found`);
    },
    header: hasHeader, // Use the first row as headers only if hasHeader is true
    skipEmptyLines: true
  });
  return false;
};

// Tag management functions
const toggleTag = (resultIndex, tag) => {
  const key = `${resultIndex}-${tag}`
  if (deselectedTags.value.has(key)) {
    deselectedTags.value.delete(key)
  } else {
    deselectedTags.value.add(key)
  }
}

const isTagDeselected = (resultIndex, tag) => {
  return deselectedTags.value.has(`${resultIndex}-${tag}`)
}

const addNewTag = (resultIndex, event) => {
  const tag = inputNewTagValue.value.trim()
  if (!tag) return

  if (!newTags.value.has(resultIndex)) {
    newTags.value.set(resultIndex, new Set())
  }

  // Check if the tag is already in the Set for this resultIndex
  const existingTags = newTags.value.get(resultIndex);
  const isTagAlreadyAdded = Array.from(existingTags).some(
    (existingTag) => existingTag.text === tag
  );

  if (isTagAlreadyAdded || (results.value[resultIndex].tags.matched_tags.includes(tag) && !isTagDeselected(resultIndex, tag))) {
    ElMessage.warning('Tag already exists in matched tags.')
    return; // If the tag already exists, do not add it again
  }

  // Check if tag exists in tagNames
  const isExistingTag = tagNames.value.includes(tag)

  newTags.value.get(resultIndex).add({
    text: tag,
    isMatched: isExistingTag
  })
  inputNewTagValue.value = ''
  searchQuery.value.set(resultIndex, '')
  showTagSuggestions.value.set(resultIndex, false)
}

const addNewTagDoubleClick = (resultIndex, tag) => {
  if (!tag) return; // Guard clause for empty tags

  if (!newTags.value.has(resultIndex)) {
    newTags.value.set(resultIndex, new Set());
  }

  // Check if the tag is already in the Set for this resultIndex
  const existingTags = newTags.value.get(resultIndex);
  const isTagAlreadyAdded = Array.from(existingTags).some(
    (existingTag) => existingTag.text === tag
  );

  if (isTagAlreadyAdded || (results.value[resultIndex].tags.matched_tags.includes(tag) && !isTagDeselected(resultIndex, tag))) {
    ElMessage.warning('Tag already exists in matched tags.')
    return; // If the tag already exists, do not add it again
  }

  // Check if tag exists in tagNames
  const isExistingTag = tagNames.value.includes(tag);

  // Add the tag to the newTags Set
  newTags.value.get(resultIndex).add({
    text: tag,
    isMatched: isExistingTag
  })
  ElMessage.success('New tag added to matched tags!')
}

const removeNewTag = (resultIndex, tagText) => {
  const tags = newTags.value.get(resultIndex)
  if (tags) {
    for (const tag of tags) {
      if (tag.text === tagText) {
        tags.delete(tag)
        break
      }
    }
  }
}

// Add new function to handle drag start
const handleDragStart = (event, tag, sourceSection) => {
  event.dataTransfer.setData('text/plain', JSON.stringify({
    tag,
    sourceSection
  }))
  fetchAllTags()
}

// Add new function to handle drag over
const handleDragOver = (event) => {
  event.preventDefault()
}

// Add new function to handle drop
const handleDrop = (event, resultIndex) => {
  event.preventDefault()
  const data = JSON.parse(event.dataTransfer.getData('text/plain'))
  const { tag, sourceSection } = data

  // Add to matched tags if not already present
  if (!results.value[resultIndex].tags.matched_tags.includes(tag) || isTagDeselected(resultIndex, tag)) {
    if (!newTags.value.has(resultIndex)) {
      newTags.value.set(resultIndex, new Set())
    }

    // Check if the tag is already in the Set for this resultIndex
    const existingTags = newTags.value.get(resultIndex);
    const isTagAlreadyAdded = Array.from(existingTags).some(
      (existingTag) => existingTag.text === tag
    );

    if (isTagAlreadyAdded) {
      ElMessage.warning('Tag already exists in matched tags.')
      return; // If the tag already exists, do not add it again
    }

    const isExistingTag = tagNames.value.includes(tag)

    newTags.value.get(resultIndex).add({
      text: tag,
      isMatched: isExistingTag,
    })
    // ElMessage.success(`Added "${tag}" to Matched Tags`)
  } else {
    ElMessage.warning('Tag already exists in matched tags.');
    return;
  }
}

// Function to fetch all tags
const fetchAllTags = async () => {
  if (hasLoadedTags.value) return // Don't fetch if already loaded

  try {
    isFetchingTags.value = true
    const response = await axios.get(`${apiAllTagsUrl.value}`)
    allTagCandidates.value = response.data
    hasLoadedTags.value = true
    ElMessage.success('Tag pool loaded successfully.')
  } catch (error) {
    console.error('Error fetching tags:', error)
    ElMessage.error('Failed to fetch tags')
    allTagCandidates.value = []
  } finally {
    isFetchingTags.value = false
  }
}

// Watch for changes to metadataSuffixEnabled
watch(metadataSuffixEnabled, async (newValue) => {
  if (newValue === true) {
    await fetchAllTags();
  }
});

// Debounced filter function for smooth typing experience
const debouncedFilter = debounce((query, resultIndex) => {
  if (!query || query.length < 2) {
    showTagSuggestions.value.set(resultIndex, false)
    return
  }
  showTagSuggestions.value.set(resultIndex, true)
}, 300)

// Get filtered tags based on search query
const getFilteredTags = (query) => {
  if (!query || query.length < 2) return []

  const searchTerm = query.toLowerCase()
  return tagNames.value
    .filter(tag => tag.toLowerCase().includes(searchTerm))
    .sort((a, b) => {
      const aLower = a.toLowerCase()
      const bLower = b.toLowerCase()
      const aStartsWith = aLower.startsWith(searchTerm)
      const bStartsWith = bLower.startsWith(searchTerm)
      if (aStartsWith && !bStartsWith) return -1
      if (!aStartsWith && bStartsWith) return 1
      return aLower.indexOf(searchTerm) - bLower.indexOf(searchTerm)
    })
    .slice(0, 40) // Limit to first 20 matches
}

// Handle input focus
const handleInputFocus = async (resultIndex) => {
  if (!hasLoadedTags.value) {
    await fetchAllTags()
  }
  showTagSuggestions.value.set(resultIndex, true)
}

// Handle input changes
const handleSearchInput = (value, resultIndex) => {
  searchQuery.value.set(resultIndex, value) // Update search query for filtering
  debouncedFilter(value, resultIndex)
}

// Handle tag selection
const selectTag = (resultIndex, tag) => {
  if (!newTags.value.has(resultIndex)) {
    newTags.value.set(resultIndex, new Set())
  }

  // Check if the tag is already in the Set for this resultIndex
  const existingTags = newTags.value.get(resultIndex);
  const isTagAlreadyAdded = Array.from(existingTags).some(
    (existingTag) => existingTag.text === tag
  );

  if (isTagAlreadyAdded || (results.value[resultIndex].tags.matched_tags.includes(tag) && !isTagDeselected(resultIndex, tag))) {
    ElMessage.warning('Tag already exists in matched tags.')
    return; // If the tag already exists, do not add it again
  }

  newTags.value.get(resultIndex).add({
    text: tag,
    isMatched: true
  })
  inputNewTagValue.value = ''
  searchQuery.value.set(resultIndex, '')
  showTagSuggestions.value.set(resultIndex, false)
}

// Close suggestions dropdown
const closeSuggestions = (resultIndex) => {
  setTimeout(() => {
    showTagSuggestions.value.set(resultIndex, false)
  }, 200)
}

// Pagination
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1 // Reset to first page when changing page size
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
}

const toggleAllResults = () => {
  if (activeNames.value.length === paginatedResults.value.length) {
    activeNames.value = []
  } else {
    activeNames.value = paginatedResults.value.map(result => result.index)
  }
}

// Function to get abstract by index
const getAbstractByIndex = (index) => {
  const item = allIndexedBiblioItems.value.find(item => item.index === index)
  return item ? item.abstract : 'Abstract not found'
}

// Download CSV
const exportFields = ref([])
const includeConceptTags = ref(true)
const includePersonOrgTags = ref(true)
const includeTimePlaceTags = ref(true)
const showExportDialog = ref(false)
const selectAll = ref(false)

const exportOptions = ref({
  fieldDelimiter: ',',
  tagDelimiter: ';',
  valueMarkers: '"'
})

const availableFields = computed(() => {
  if (allIndexedBiblioItems.value.length === 0) return []

  return Object.keys(allIndexedBiblioItems.value[0])
    .filter(key => key !== 'index')
    .filter(key => allIndexedBiblioItems.value.some(item => {
      const value = item[key]
      return value !== null && value !== undefined && String(value).trim() !== ''
    }))
})

const openExportDialog = () => {
  exportFields.value = [...availableFields.value]
  selectAll.value = true
  showExportDialog.value = true
}

const handleSelectAll = (val) => {
  if (val) {
    exportFields.value = [...availableFields.value]
  } else {
    exportFields.value = []
  }
}

const handleExport = () => {
  downloadCSV()
  showExportDialog.value = false
}

const downloadCSV = () => {
  // If the user directly clicked the export button, the exportFields array would be empty. So it should be set to all available fields by default.
  if (exportFields.value.length === 0) {
    exportFields.value = [...availableFields.value];
  }

  const { fieldDelimiter, tagDelimiter, valueMarkers } = exportOptions.value;
  const startMarker = valueMarkers || '';
  const endMarker = valueMarkers || startMarker;

  const csvData = results.value.map(result => {
    const item = allIndexedBiblioItems.value.find(item => item.index === result.index);
    const row = exportFields.value.map(field => {
      // Replace undefined or null field values with an empty string
      let fieldValue = item[field] != null ? item[field] : '';

      // If fieldValue is not a string and is an object or array (e.g. a list of several authors), expand it
      if (isReactive(fieldValue) || Array.isArray(fieldValue)) {
        const rawFieldValue = toRaw(fieldValue); // Convert to raw object/array

        if (Array.isArray(rawFieldValue)) {
          // Check for array of objects with firstName or lastName
          fieldValue = rawFieldValue
            .map(entry => {
              if (entry.lastName || entry.firstName) {
                // Use empty string if one of the values is missing
                const lastName = entry.lastName || '';
                const firstName = entry.firstName || '';
                return `${lastName},${firstName}`.trim();
              }
              return ''; // Skip invalid entries
            })
            .filter(Boolean) // Remove empty strings
            .join(tagDelimiter); // Join with tagDelimiter
        }
      } else if (typeof fieldValue === 'object') {
        // Handle plain objects by joining values with tagDelimiter
        fieldValue = Object.values(fieldValue).join(tagDelimiter);
      } else if (typeof fieldValue !== 'string') {
        // Convert other non-string values to strings
        fieldValue = String(fieldValue);
      }
      return `${startMarker}${fieldValue}${endMarker}`;
    });

    // Decorate active matched tags (those matched tags that are still active (not de-selected) after being edited by the user)
    const decoratedActiveMatchedTags = result.tags.matched_tags
      .filter(tag => !isTagDeselected(result.index, tag))
      .map(tagText => {
        const matchingTag = allTagCandidates.value.find(t => t.name === tagText);

        // Add metadata suffix if metadataSuffixEnabled is true and tag exists in allTagCandidates
        let decoratedTagText = '';
        if (metadataSuffixEnabled.value && matchingTag) {
          const metadataSuffix = selectedMetadataFields.value
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|');
          if (metadataSuffix) {
            decoratedTagText = `${tagText} [${metadataSuffix}]`;
          }
        }

        // Add custom suffix to all tags if enabled
        if (customSuffixEnabled.value) {
          decoratedTagText = `${decoratedTagText}${customSuffix.value}`;
        }

        return decoratedTagText;
      });

    // Process newly added customized tags (tags added by user and not in the tag pool)
    const decoratedNewTags = Array.from(newTags.value.get(result.index) || [])
      .map(tag => {
        let decoratedNewTagText = tag.text;
        if (newTagMarkEnabled.value && !tag.isMatched) {
          if (newTagMarkPosition.value === 'prefix') {
            decoratedNewTagText = `${newTagMark.value}${tag.text}`;
          } else if (newTagMarkPosition.value === 'suffix') {
            decoratedNewTagText = `${tag.text}${newTagMark.value}`;
          }
        }

        const matchingTag = allTagCandidates.value.find(t => t.name === tag.text);

        // Add metadata suffix if enabled and tag exists in allTagCandidates
        if (metadataSuffixEnabled.value && matchingTag) {
          const metadataSuffix = selectedMetadataFields.value
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|');
          if (metadataSuffix) {
            decoratedNewTagText = `${decoratedNewTagText} [${metadataSuffix}]`;
          }
        }

        // Add custom suffix if enabled
        if (customSuffixEnabled.value) {
          decoratedNewTagText = `${decoratedNewTagText}${customSuffix.value}`;
        }

        return decoratedNewTagText;
      });

    // Combine all tags and add processed tag if enabled
    const allDecoratedTags = [...new Set([...decoratedActiveMatchedTags, ...decoratedNewTags])];
    if (processedTagEnabled.value && processedTag.value) {
      allDecoratedTags.push(processedTag.value);
    }

    // Add combined tags to row
    row.push(`${startMarker}${allDecoratedTags.join(tagDelimiter)}${endMarker}`);

    // Add other tag categories if selected
    if (includeConceptTags.value) row.push(`${startMarker}${result.tags.concept_tags.join(tagDelimiter)}${endMarker}`);
    if (includePersonOrgTags.value) row.push(`${startMarker}${result.tags.person_org_tags.join(tagDelimiter)}${endMarker}`);
    if (includeTimePlaceTags.value) row.push(`${startMarker}${result.tags.time_place_tags.join(tagDelimiter)}${endMarker}`);

    return row.join(fieldDelimiter);
  });

  // Create header row
  const headerRow = [
    ...exportFields.value,
    'Matched Tags',
    ...(includeConceptTags.value ? ['Concept Tags'] : []),
    ...(includePersonOrgTags.value ? ['Person/Org Tags'] : []),
    ...(includeTimePlaceTags.value ? ['Time/Place Tags'] : [])
  ].map(header => `${startMarker}${header}${endMarker}`);

  // Add header row to csvData
  csvData.unshift(headerRow.join(fieldDelimiter));

  const csv = csvData.join('\n');
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = 'tagged_items.csv';
  link.click();
};

// Save matched tags to Zotero
const saveTagsToZotero = async () => {
  if (!isZoteroConfigValid.value || currentSource.value !== 'Zotero') {
    ElMessage.error('Can only save to Zotero for items imported from Zotero')
    return
  }

  try {
    const { libraryId, apiKey } = zoteroConfig.value
    let successCount = 0
    let failureCount = 0

    const updatePromises = results.value.map(async (result) => {
      const item = allIndexedBiblioItems.value.find(item => item.index === result.index)
      if (!item?.key || !item?.version) return null

      try {
        // Fetch the current item to get its existing tags
        const currentItemResponse = await axios.get(
          `${zoteroBaseUrl.value}/items/${item.key}`,
          {
            headers: {
              'Zotero-API-Version': '3',
              'Authorization': `Bearer ${apiKey}`
            }
          }
        )

        // Get existing tags, excluding the tag used for import and any previously added suffixes
        const existingTags = currentItemResponse.data.data.tags
          .filter(tagObj => {
            const tag = tagObj.tag
            return tag !== zoteroConfig.value.tag &&
              (!customSuffixEnabled.value || !tag.endsWith(customSuffix.value)) &&
              (!processedTagEnabled.value || tag !== processedTag.value)
          })
          .map(tagObj => tagObj.tag)

        // Process matched tags with metadata and custom suffixes
        const decoratedActiveMatchedTags = result.tags.matched_tags
          .filter(tag => !isTagDeselected(result.index, tag))
          .map(tagName => {
            let decoratedTag = tagName
            const matchingTag = allTagCandidates.value.find(t => t.name === tagName)

            // Add metadata suffix if enabled and tag exists in allTagCandidates
            if (metadataSuffixEnabled.value && matchingTag) {
              const metadataSuffix = selectedMetadataFields.value
                .map(field => `${matchingTag[field]}`)
                .filter(Boolean)
                .join('|')
              if (metadataSuffix) {
                decoratedTag = `${decoratedTag} [${metadataSuffix}]`
              }
            }

            // Add custom suffix if enabled
            if (customSuffixEnabled.value) {
              decoratedTag = `${decoratedTag}${customSuffix.value}`
            }

            return decoratedTag
          })

        // Process new tags with metadata and custom suffixes
        const decoratedNewTags = Array.from(newTags.value.get(result.index) || [])
          .map(tag => {
            let tagText = tag.text
            // Add new tag mark if enabled
            if (newTagMarkEnabled.value && !tag.isMatched) {
              if (newTagMarkPosition.value === 'prefix') {
                tagText = `${newTagMark.value}${tagText}`
              } else if (newTagMarkPosition.value === 'suffix') {
                tagText = `${tagText}${newTagMark.value}`
              }
            }

            let decoratedTag = tagText
            const matchingTag = allTagCandidates.value.find(t => t.name === tag.text)

            // Add metadata suffix if enabled and tag exists in allTagCandidates
            if (metadataSuffixEnabled.value && matchingTag) {
              const metadataSuffix = selectedMetadataFields.value
                .map(field => `${matchingTag[field]}`)
                .filter(Boolean)
                .join('|')
              if (metadataSuffix) {
                decoratedTag = `${decoratedTag} [${metadataSuffix}]`
              }
            }

            // Add custom suffix if enabled
            if (customSuffixEnabled.value) {
              decoratedTag = `${decoratedTag}${customSuffix.value}`
            }

            return decoratedTag
          })

        // Combine all tags
        const allDecoratedTags = [...new Set([
          ...existingTags,
          ...decoratedActiveMatchedTags,
          ...decoratedNewTags
        ])]

        // Format tags for Zotero API
        const tagsToUpdate = allDecoratedTags.map(tag => ({ tag }))

        // // Add back the import tag
        // if (zoteroConfig.value.tag) {
        //   tagsToUpdate.push({ tag: zoteroConfig.value.tag })
        // }

        // Add the processed tag if enabled
        if (processedTagEnabled.value && processedTag.value) {
          tagsToUpdate.push({ tag: processedTag.value })
        }

        // Update the item with combined tags
        await axios.patch(
          `${zoteroBaseUrl.value}/items/${item.key}`,
          {
            tags: tagsToUpdate
          },
          {
            headers: {
              'Zotero-API-Version': '3',
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json',
              'If-Unmodified-Since-Version': currentItemResponse.data.version
            }
          }
        )
        successCount++
      } catch (error) {
        console.error(`Error updating item ${item.key}:`, error)
        failureCount++
        throw error
      }
    })

    await Promise.all(updatePromises)

    if (failureCount === 0) {
      ElMessage.success(`Successfully saved tags to Zotero for all ${successCount} items`)
    } else {
      ElMessage.warning(`Saved tags for ${successCount} items, failed for ${failureCount} items`)
    }
  } catch (error) {
    console.error('Error saving tags to Zotero:', error)
    let errorMessage = 'Failed to save tags to Zotero'

    if (error.response) {
      switch (error.response.status) {
        case 403:
          errorMessage = 'Invalid API key or insufficient permissions'
          break
        case 404:
          errorMessage = 'Library or item not found'
          break
        case 412:
          errorMessage = 'Item was modified since last retrieval. Please refresh and try again'
          break
        case 429:
          errorMessage = 'Too many requests. Please try again later'
          break
        default:
          errorMessage = error.response.data?.message || errorMessage
      }
    }

    ElMessage.error(errorMessage)
  }
}
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
}

.el-divider--nowrap {
  text-align: center;
  white-space: nowrap;
  padding: 0;
  width: 100%;
  max-width: 100%;
}

.el-input-delimiter {
  width: 2rem;
}

.el-pagination {
  flex-wrap: wrap;
  gap: 5px;
}

.mark-position-select {
  width: 4rem;
  height: 1.8rem;
}

.center-content {
  display: flex;
  justify-content: center;
  /* Horizontally center child elements */
  align-items: center;
  /* Vertically center child elements */
  gap: 5px;
  /* Add spacing between child elements */
  flex-wrap: wrap;
  /* Ensure responsiveness if elements exceed available space */
  text-align: center;
}

.custom-input-number {
  width: 80px;
}

.button-container {
  display: flex;
  justify-content: center;
  /* Ensures the content inside <el-col> is centered */
  align-items: center;
  /* Aligns the button vertically within the container */
}

.page-container {
  min-height: 100vh;
  /* height: 100%; */
  width: 100%;
  display: flex;
  justify-content: center;
}

.main-container {
  width: 100%;
  max-width: 1200px;
  padding: 20px;
}

.app-container {
  width: 100%;
  height: 100%;
  margin: auto auto;
}


.header-title {
  margin: 0;
  font-size: 24px;
  color: var(--el-text-color-primary);
}

.article-form {
  width: 100%;
}

.article-input {
  margin-bottom: 30px;
  padding: 20px;
  border-radius: 8px;
  background-color: var(--el-bg-color);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 20px;
}

.title-input,
.abstract-input {
  width: 100%;
}

.divider-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin: 20px 0;
  justify-content: center;
}

.csv-submit-button {
  margin-top: 20px;
}

.loading-message {
  text-align: center;
  margin: 20px 0;
  padding: 20px;
  background-color: var(--el-color-primary-light-9);
  border-radius: 4px;
  color: var(--el-text-color-primary);
}

.results {
  margin-top: 10px;
}

.wrap-text {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag-item {
  margin: 0;
}

.el-tag {
  white-space: normal; /* Allow text to wrap */
  word-break: break-word; /* Break long words if necessary */
  min-height: 1.5rem;
  height: fit-content;
  max-width: 100%; /* Ensure the tag doesn't overflow its container */
}


/* For tag matching */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.loading-icon {
  width: 14px;
  height: 14px;
  animation: rotate 1s linear infinite;
}

.tag-input-container {
  position: relative;
  display: inline-block;
}

.tag-suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: flex;
  max-height: 300px;
  background: white;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto;
}

.tag-suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-suggestion-item:hover {
  background-color: var(--el-color-primary-light-9);
}

.loading-indicator {
  padding: 8px 12px;
  color: var(--el-text-color-secondary);
  text-align: center;
}

/* Add new styles for drag and drop */
.draggable-tag {
  cursor: move;
  transition: transform 0.2s;
}

.draggable-tag:hover {
  transform: scale(1.05);
}

.draggable-tag:active {
  cursor: grabbing;
}

.droppable-area {
  min-height: 40px;
  padding: 8px;
  border: 2px dashed transparent;
  border-radius: 4px;
  transition: all 0.3s;
}

.droppable-area:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

/* New styles for CSV preview */
.csv-section {
  margin: 20px 0;
  padding: 20px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
}

.csv-preview {
  margin-top: 20px;
}

.preview-item {
  padding: 8px;
  margin: 4px 0;
  background-color: var(--el-bg-color);
  border-radius: 4px;
}

.preview-title {
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.more-items {
  margin-top: 8px;
  color: var(--el-text-color-secondary);
  font-style: italic;
}

.download-section {
  margin-top: 20px;
  text-align: center;
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.deselected-tag {
  opacity: 0.5;
  text-decoration: line-through;
}

.new-tag {
  background-color: var(--el-color-success-light-9);
  border-color: var(--el-color-success);
  color: var(--el-color-success);
}

.tag-input {
  width: 280px;
  max-width: 100%;
  margin-left: 8px;
}

.floating-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 10px;
}

.floating-button .el-button {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* Optional: Add hover effect for clickable tags */
.clickable-tag:hover {
  transform: scale(1.05);
}

.truncate-title {
  /* display: inline-block; */
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.el-form-item__label) {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}

:deep(.el-card__header) {
  text-align: center;
  padding: 10px;
  background-color: var(--el-color-primary-light-9);
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
  /* font-weight: bold; */
}

:deep(.el-descriptions__label) {
  font-weight: bold;
  width: 200px;
  padding-right: 16px;
}

:deep(.el-textarea__inner) {
  min-height: 120px !important;
}

:deep(.el-main) {
  padding: 0;
}

:deep(.el-descriptions__table) {
  width: 100%; /* Ensure the table takes full width of its container */
  max-width: 100%; /* Prevent overflow */
  table-layout: fixed; /* Ensure the table respects column widths */
}

:deep(.el-descriptions__cell) {
  word-break: break-word; /* Break long words to prevent overflow */
  white-space: normal; /* Allow text to wrap */
}


/* zotero-related */
.zotero-form {
  padding: 30px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 0px;
  margin-bottom: 0px;
}

.source-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.fetch-button {
  width: 100%;
  margin-top: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
}

:deep(.el-form-item__label) {
  font-weight: bold;
}
</style>