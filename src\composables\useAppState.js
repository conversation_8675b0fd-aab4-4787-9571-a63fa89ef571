import { ref, computed, watch } from 'vue'
import { API_ENDPOINTS, MODEL_OPTIONS, EXPORT_CONFIG } from '../utils/constants.js'

/**
 * Main application state management composable
 * Manages global application state and configuration
 */
export function useAppState() {
  // API Configuration
  const apiUrl = ref(API_ENDPOINTS.GENERATE_TAGS)
  const apiAllTagsUrl = ref(API_ENDPOINTS.ALL_TAGS)
  const selectedModel = ref('gpt-4o-mini')
  const modelOptions = ref(MODEL_OPTIONS)

  // UI State
  const drawerVisible = ref(false)
  const screenIsPortrait = ref(window.innerHeight > window.innerWidth)
  const activeDataSource = ref(['csv'])

  // Data State
  const biblioItems = ref([])
  const allIndexedBiblioItems = ref([])
  const results = ref([])
  const removedItems = ref([])
  const currentSource = ref('')

  // Loading States
  const isLoading = ref(false)
  const isCSVLoading = ref(false)
  const isZoteroLoading = ref(false)
  const isFetchingTags = ref(false)

  // Timer States
  const elapsedTime = ref(0)
  const elapsedCSVTime = ref(0)
  const fetchProgress = ref(0)

  // Export Configuration
  const newTagMarkEnabled = ref(true)
  const newTagMarkPosition = ref('suffix')
  const newTagMark = ref(EXPORT_CONFIG.DEFAULT_NEW_TAG_MARK)
  const metadataSuffixEnabled = ref(true)
  const selectedMetadataFields = ref(['record_id'])
  const customSuffixEnabled = ref(false)
  const customSuffix = ref(EXPORT_CONFIG.DEFAULT_CUSTOM_SUFFIX)
  const processedTagEnabled = ref(true)
  const processedTag = ref(EXPORT_CONFIG.DEFAULT_PROCESSED_TAG)

  // Computed Properties
  const drawerDirection = computed(() => {
    return screenIsPortrait.value ? 'btt' : 'rtl'
  })

  const hasData = computed(() => biblioItems.value.length > 0)
  const hasResults = computed(() => results.value.length > 0)
  const isProcessing = computed(() => isLoading.value || isCSVLoading.value || isZoteroLoading.value)

  // Methods
  const updateScreenOrientation = () => {
    screenIsPortrait.value = window.innerHeight > window.innerWidth
  }

  const clearAllData = () => {
    biblioItems.value = []
    allIndexedBiblioItems.value = []
    results.value = []
    removedItems.value = []
    currentSource.value = ''
    fetchProgress.value = 0
  }

  const setApiUrls = (taggingUrl, tagPoolUrl) => {
    if (taggingUrl) apiUrl.value = taggingUrl
    if (tagPoolUrl) apiAllTagsUrl.value = tagPoolUrl
  }

  const setLoadingState = (type, state) => {
    switch (type) {
      case 'csv':
        isCSVLoading.value = state
        break
      case 'zotero':
        isZoteroLoading.value = state
        break
      case 'tags':
        isFetchingTags.value = state
        break
      default:
        isLoading.value = state
    }
  }

  const updateProgress = (progress) => {
    fetchProgress.value = Math.round(progress)
  }

  const updateTimer = (type, time) => {
    if (type === 'csv') {
      elapsedCSVTime.value = time
    } else {
      elapsedTime.value = time
    }
  }

  // Watch for screen resize
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', updateScreenOrientation)
  }

  return {
    // API Configuration
    apiUrl,
    apiAllTagsUrl,
    selectedModel,
    modelOptions,

    // UI State
    drawerVisible,
    screenIsPortrait,
    drawerDirection,
    activeDataSource,

    // Data State
    biblioItems,
    allIndexedBiblioItems,
    results,
    removedItems,
    currentSource,

    // Loading States
    isLoading,
    isCSVLoading,
    isZoteroLoading,
    isFetchingTags,
    isProcessing,

    // Timer States
    elapsedTime,
    elapsedCSVTime,
    fetchProgress,

    // Export Configuration
    newTagMarkEnabled,
    newTagMarkPosition,
    newTagMark,
    metadataSuffixEnabled,
    selectedMetadataFields,
    customSuffixEnabled,
    customSuffix,
    processedTagEnabled,
    processedTag,

    // Computed Properties
    hasData,
    hasResults,

    // Methods
    updateScreenOrientation,
    clearAllData,
    setApiUrls,
    setLoadingState,
    updateProgress,
    updateTimer
  }
}
