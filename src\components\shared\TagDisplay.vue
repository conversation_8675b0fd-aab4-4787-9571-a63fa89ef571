<template>
  <el-tag
    :type="tagType"
    :class="tagClasses"
    :closable="closable"
    @close="$emit('close')"
    @click="$emit('click')"
    @dblclick="$emit('dblclick')"
    :draggable="draggable"
    @dragstart="$emit('dragstart', $event)"
  >
    {{ displayText }}
  </el-tag>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  text: {
    type: String,
    required: true
  },
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'success', 'warning', 'danger', 'info'].includes(value)
  },
  deselected: {
    type: Boolean,
    default: false
  },
  clickable: {
    type: Boolean,
    default: false
  },
  draggable: {
    type: Boolean,
    default: false
  },
  closable: {
    type: Boolean,
    default: false
  },
  maxLength: {
    type: Number,
    default: null
  }
})

const emit = defineEmits(['close', 'click', 'dblclick', 'dragstart'])

const tagType = computed(() => {
  if (props.deselected) return 'info'
  return props.type
})

const tagClasses = computed(() => {
  return {
    'tag-item': true,
    'deselected-tag': props.deselected,
    'clickable-tag': props.clickable,
    'draggable-tag': props.draggable
  }
})

const displayText = computed(() => {
  if (props.maxLength && props.text.length > props.maxLength) {
    return props.text.substring(0, props.maxLength) + '...'
  }
  return props.text
})
</script>

<style scoped>
.tag-item {
  margin: 0;
  white-space: normal;
  word-break: break-word;
  min-height: 1.5rem;
  height: fit-content;
  max-width: 100%;
}

.deselected-tag {
  opacity: 0.5;
  text-decoration: line-through;
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.clickable-tag:hover {
  transform: scale(1.05);
}

.draggable-tag {
  cursor: move;
  transition: transform 0.2s;
}

.draggable-tag:hover {
  transform: scale(1.05);
}

.draggable-tag:active {
  cursor: grabbing;
}
</style>
